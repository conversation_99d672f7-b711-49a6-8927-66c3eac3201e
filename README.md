# 自动化测试管理系统

这是一个功能完善的自动化测试管理系统，支持移动应用的自动化测试管理、执行和报告生成。

## 主要功能

1. 用户管理和权限控制
   - 用户注册、登录、修改
   - 基于角色的权限管理
   - JWT token认证

2. 项目管理
   - 创建、修改、删除项目
   - 项目列表查看
   - 项目权限控制

3. 测试用例管理
   - 创建、修改、删除测试用例
   - 测试步骤配置
   - 按项目筛选测试用例
   - 测试用例复用

4. 页面元素管理
   - 支持多种元素定位方式（ID、Name、XPath等）
   - 元素操作配置（点击、输入、滑动等）
   - 按项目筛选页面元素
   - 元素库管理

5. 测试执行
   - 基于Appium的自动化测试执行
   - 测试结果实时反馈
   - 截图和日志记录
   - 失败重试机制

6. 定时任务
   - 定时执行测试用例
   - 任务状态管理
   - 执行历史记录
   - 自定义执行计划

7. 测试报告
   - HTML格式的测试报告
   - 测试结果统计和分析
   - 失败用例分析
   - 趋势分析

8. 邮件通知
   - 测试报告邮件发送
   - 支持多收件人
   - 自定义通知模板
   - 包含截图附件

## 技术栈

- 后端：Python + FastAPI + SQLAlchemy
- 前端：React + Ant Design
- 数据库：PostgreSQL
- 自动化测试：Appium + Selenium
- 任务调度：APScheduler
- 容器化：Docker

## 系统要求

- Python 3.9+
- PostgreSQL 12+
- Node.js 14+
- Appium 2.0+
- Android SDK（用于Android测试）
- Xcode（用于iOS测试，仅macOS）

## 安装步骤

1. 克隆项目
```bash
git clone <repository_url>
cd auto_test
```

2. 安装Python依赖
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
.\venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

3. 安装Appium
```bash
npm install -g appium
appium driver install uiautomator2
appium driver install xcuitest  # 仅macOS
```

4. 配置环境变量
创建 `.env` 文件在 backend 目录下：
```env
# 数据库配置
POSTGRES_USER=your_db_user
POSTGRES_PASSWORD=your_db_password
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_DB=auto_test

# JWT配置
SECRET_KEY=your-secret-key-here

# 邮件配置
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password

# Appium配置
APPIUM_HOST=127.0.0.1
APPIUM_PORT=4723
```

5. 初始化数据库
```bash
# 确保PostgreSQL服务已启动
psql -U postgres
CREATE DATABASE auto_test;
```

6. 启动系统
```bash
# 启动后端服务
cd backend

# 激活虚拟环境
source venv/bin/activate  # Linux/macOS
# 或
.\venv\Scripts\activate  # Windows

# 确保安装了所有依赖
pip install -r requirements.txt

# 确保目录结构正确：
# backend/
# ├── app/
# │   ├── main.py
# │   └── ...
# ├── venv/
# └── requirements.txt

# 设置Python路径（重要！）
export PYTHONPATH=$PYTHONPATH:$(pwd)  # Linux/macOS
# 或
set PYTHONPATH=%PYTHONPATH%;%cd%  # Windows

# 方法1 使用python -m（推荐）
# cd /Users/<USER>/olight/workspace_olight/auto_test/backend && cd app && PYTHONPATH=$PWD/.. python main.py
cd backend && python -m uvicorn app.main:app --reload --port 8001

# 如果遇到模块导入错误，请确保：
# 1. 当前目录是backend/
# 2. 已设置PYTHONPATH
# 3. app/main.py文件存在
# 4. 所有依赖都已正确安装

# 启动Appium服务
appium
```

## 系统启动步骤

1. 启动数据库服务
```bash
# macOS
brew services start postgresql
# Linux
sudo service postgresql start
```

2. 启动后端服务
```bash
# 进入后端目录
cd backend

# 激活虚拟环境
source venv/bin/activate  # Linux/macOS
# 或
.\venv\Scripts\activate  # Windows

# 启动FastAPI服务
uvicorn app.main:app --reload --port 8001
```

3. 启动Appium服务
```bash
# 新开一个终端
appium
```

4. 检查服务状态
- 数据库服务：默认运行在 localhost:5432
- 后端API服务：http://localhost:8001
- Appium服务：默认运行在 localhost:4723
- API文档访问：http://localhost:8001/docs

5. 常见启动问题处理
- 端口占用：修改对应服务的端口号
- 数据库连接失败：检查PostgreSQL服务状态和连接参数
- Appium启动失败：检查Node.js环境和Appium安装状态
- 后端服务启动失败：检查虚拟环境和依赖安装

## API文档

启动系统后，可以通过以下地址访问API文档：
- Swagger UI: http://localhost:8001/docs
- ReDoc: http://localhost:8001/redoc

## 使用指南

1. 用户管理
   - 首次启动系统会自动创建超级管理员账户
   - 使用超级管理员账户创建其他用户
   - 分配用户角色和权限

2. 项目管理
   - 创建新项目
   - 配置项目基本信息
   - 设置项目权限

3. 元素管理
   - 添加页面元素
   - 配置元素定位方式
   - 设置元素操作属性

4. 测试用例管理
   - 创建测试用例
   - 配置测试步骤
   - 设置测试数据

5. 执行测试
   - 手动执行测试
   - 配置定时任务
   - 查看测试报告

## 常见问题

1. 数据库连接问题
   - 检查PostgreSQL服务是否启动
   - 验证数据库用户名和密码
   - 确认数据库端口是否正确

2. Appium相关问题
   - 检查Appium服务是否启动
   - 确认设备连接状态
   - 验证Android/iOS SDK配置

3. 权限问题
   - 检查用户角色配置
   - 验证JWT token
   - 确认API访问权限

## 开发指南

1. 代码结构
```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py                # Flask应用入口，只负责初始化和注册路由
│   ├── tools.py               # 通用工具方法（如检测设备等）
│   ├── get_page_element.py    # PageElementFetcher类，负责获取设备元素
│   ├── router_tools.py        # RouterTools类，注册和管理所有路由
│   ├── core/
│   │   └── device_manager.py  # 设备管理相关类
│   ├── models/
│   │   └── ...                # ORM模型
│   ├── routes/
│   │   └── ...                # 其他路由（如有更细粒度可拆分）
│   ├── templates/
│   │   └── ...                # Jinja2模板
│   ├── static/
│   │   └── ...                # 静态资源
│   ├── data/
│   │   └── ...                # 数据文件（如有）
│   └── schemas/
│       └── ...                # Pydantic/Marshmallow等schema
├── api/
│   └── ...                # 其他API相关
└── requirements.txt  # 依赖文件
```

2. 添加新功能
   - 创建数据库模型
   - 添加Pydantic schema
   - 实现API路由
   - 编写单元测试

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 发起Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系开发团队。 

## 连接设备出错的情况：
1. Appium服务器正在运行
 - adb devices
2. Android设备已连接并已授权
   - curl http://localhost:4723/wd/hub/status
3. 应用程序已正确安装