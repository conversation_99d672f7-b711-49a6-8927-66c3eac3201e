# 🧪 元素索引功能验证测试

## 🎯 测试目标
验证修复后的元素索引功能是否正确工作，包括：
1. 智能自动分配索引
2. 用户手动指定索引
3. 混合使用场景

## 📋 测试用例设计

### 测试用例1：智能自动分配（默认行为）
```
用例名称：智能索引分配测试
描述：测试系统自动为相同ID元素分配索引

步骤1：设置元素文本内容
- 操作类型：设置元素文本内容
- 选择器类型：ID
- 选择器值：com.olight.omall:id/edt_view
- 第几个元素：1 (保持默认值)
- 要设置的文本内容：自动分配第一个

步骤2：设置元素文本内容
- 操作类型：设置元素文本内容
- 选择器类型：ID
- 选择器值：com.olight.omall:id/edt_view
- 第几个元素：1 (保持默认值)
- 要设置的文本内容：自动分配第二个
```

**期望日志输出：**
```
[智能索引] 为ID 'com.olight.omall:id/edt_view' 自动分配索引: 0
[智能索引] 为ID 'com.olight.omall:id/edt_view' 自动分配索引: 1
```

### 测试用例2：用户手动指定索引
```
用例名称：手动索引指定测试
描述：测试用户手动指定元素索引

步骤1：设置元素文本内容
- 操作类型：设置元素文本内容
- 选择器类型：ID
- 选择器值：com.olight.omall:id/edt_view
- 第几个元素：2 (手动指定第2个)
- 要设置的文本内容：手动指定第二个

步骤2：设置元素文本内容
- 操作类型：设置元素文本内容
- 选择器类型：ID
- 选择器值：com.olight.omall:id/edt_view
- 第几个元素：1 (手动指定第1个)
- 要设置的文本内容：手动指定第一个
```

**期望日志输出：**
```
[用户指定] ID 'com.olight.omall:id/edt_view' 使用用户指定索引: 1
[用户指定] ID 'com.olight.omall:id/edt_view' 使用用户指定索引: 0
```

### 测试用例3：混合使用场景
```
用例名称：混合索引使用测试
描述：测试智能分配和手动指定混合使用

步骤1：设置元素文本内容
- 操作类型：设置元素文本内容
- 选择器类型：ID
- 选择器值：com.olight.omall:id/edt_view
- 第几个元素：1 (默认值，触发智能分配)
- 要设置的文本内容：智能分配

步骤2：设置元素文本内容
- 操作类型：设置元素文本内容
- 选择器类型：ID
- 选择器值：com.olight.omall:id/edt_view
- 第几个元素：3 (手动指定第3个)
- 要设置的文本内容：手动指定第三个

步骤3：设置元素文本内容
- 操作类型：设置元素文本内容
- 选择器类型：ID
- 选择器值：com.olight.omall:id/edt_view
- 第几个元素：1 (默认值，但由于之前已使用过该ID，触发智能分配)
- 要设置的文本内容：继续智能分配
```

**期望日志输出：**
```
[智能索引] 为ID 'com.olight.omall:id/edt_view' 自动分配索引: 0
[用户指定] ID 'com.olight.omall:id/edt_view' 使用用户指定索引: 2
[智能索引] 为ID 'com.olight.omall:id/edt_view' 自动分配索引: 1
```

## 🔍 验证步骤

### 1. 前端数据检查
在浏览器开发者工具中检查提交的JSON：

**测试用例1的JSON：**
```json
{
  "steps": [
    {
      "type": "set_text",
      "params": {
        "selectorType": "id",
        "selectorValue": "com.olight.omall:id/edt_view",
        "inputValue": "自动分配第一个",
        "elementIndex": 0
      }
    },
    {
      "type": "set_text", 
      "params": {
        "selectorType": "id",
        "selectorValue": "com.olight.omall:id/edt_view",
        "inputValue": "自动分配第二个",
        "elementIndex": 0
      }
    }
  ]
}
```

**测试用例2的JSON：**
```json
{
  "steps": [
    {
      "type": "set_text",
      "params": {
        "selectorType": "id",
        "selectorValue": "com.olight.omall:id/edt_view",
        "inputValue": "手动指定第二个",
        "elementIndex": 1  // 前端2转换为后端1
      }
    },
    {
      "type": "set_text",
      "params": {
        "selectorType": "id", 
        "selectorValue": "com.olight.omall:id/edt_view",
        "inputValue": "手动指定第一个",
        "elementIndex": 0  // 前端1转换为后端0
      }
    }
  ]
}
```

### 2. 后端日志检查
执行测试用例时，观察控制台输出的日志，确认：
- 智能分配时显示 `[智能索引]`
- 用户指定时显示 `[用户指定]`
- 索引值正确

### 3. 设备操作验证
在实际设备上验证：
- 文本是否输入到正确的输入框
- 操作顺序是否符合预期

## ✅ 成功标准

1. **智能分配正确**：相同ID的元素按顺序自动分配索引0、1、2...
2. **用户指定生效**：手动指定的索引值被正确使用
3. **混合使用正常**：智能分配和手动指定可以混合使用
4. **日志输出准确**：能清楚区分智能分配和用户指定
5. **设备操作正确**：实际操作结果符合预期

## 🐛 问题排查

如果测试失败，检查：
1. 前端索引转换是否正确（1基→0基）
2. 后端逻辑判断是否准确
3. 执行时索引值是否正确传递
4. 设备管理器是否正确使用索引

通过这些测试，我们可以确认元素索引功能已经完全正常工作！
