# 🧪 测试元素索引功能

## 测试目标
验证"第几个元素"输入框功能是否正确工作，包括：
1. 前端正确收集用户输入的索引值
2. 后端正确接收并使用索引值
3. 执行时能准确操作指定索引的元素

## 🔍 调试步骤

### 1. 创建测试用例
```
测试用例名称：元素索引测试
描述：测试相同ID元素的索引选择

步骤1：设置元素文本内容
- 操作类型：设置元素文本内容
- 选择器类型：ID
- 选择器值：com.olight.omall:id/edt_view
- 第几个元素：1
- 要设置的文本内容：第一个输入框

步骤2：设置元素文本内容
- 操作类型：设置元素文本内容
- 选择器类型：ID
- 选择器值：com.olight.omall:id/edt_view
- 第几个元素：2
- 要设置的文本内容：第二个输入框
```

### 2. 检查前端数据收集
在浏览器开发者工具中查看提交的JSON数据：
```json
{
  "name": "元素索引测试",
  "description": "测试相同ID元素的索引选择",
  "steps": [
    {
      "order": 1,
      "type": "set_text",
      "params": {
        "selectorType": "id",
        "selectorValue": "com.olight.omall:id/edt_view",
        "inputValue": "第一个输入框",
        "elementIndex": 0  // 应该是0（前端1转换为后端0）
      }
    },
    {
      "order": 2,
      "type": "set_text",
      "params": {
        "selectorType": "id",
        "selectorValue": "com.olight.omall:id/edt_view",
        "inputValue": "第二个输入框",
        "elementIndex": 1  // 应该是1（前端2转换为后端1）
      }
    }
  ]
}
```

### 3. 检查后端日志输出
执行测试用例时，应该看到以下日志：

#### 智能索引分配阶段：
```
[用户指定] ID 'com.olight.omall:id/edt_view' 使用用户指定索引: 0
[用户指定] ID 'com.olight.omall:id/edt_view' 使用用户指定索引: 1
```

#### 执行阶段：
```
[DEBUG] set_text step: {'type': 'set_text', 'params': {'selectorType': 'id', 'selectorValue': 'com.olight.omall:id/edt_view', 'inputValue': '第一个输入框', 'elementIndex': 0}}
[DEBUG] set_text params: {'selectorType': 'id', 'selectorValue': 'com.olight.omall:id/edt_view', 'inputValue': '第一个输入框', 'elementIndex': 0}
[DEBUG] 最终使用的元素索引: 0

[DEBUG] set_text step: {'type': 'set_text', 'params': {'selectorType': 'id', 'selectorValue': 'com.olight.omall:id/edt_view', 'inputValue': '第二个输入框', 'elementIndex': 1}}
[DEBUG] set_text params: {'selectorType': 'id', 'selectorValue': 'com.olight.omall:id/edt_view', 'inputValue': '第二个输入框', 'elementIndex': 1}
[DEBUG] 最终使用的元素索引: 1
```

### 4. 验证设备操作
在设备上应该看到：
- 第一个输入框显示："第一个输入框"
- 第二个输入框显示："第二个输入框"

## 🐛 可能的问题排查

### 问题1：前端索引转换不正确
**症状**：后端收到的 elementIndex 不是期望值
**检查**：
- 浏览器开发者工具 Network 标签页
- 查看提交的 JSON 数据中的 elementIndex 字段

### 问题2：后端没有使用用户指定的索引
**症状**：日志显示"智能索引"而不是"用户指定"
**检查**：
- 确认前端提交的数据包含 elementIndex 字段
- 检查后端 execute_test_steps 函数的逻辑

### 问题3：执行时索引值丢失
**症状**：执行阶段的索引值不正确
**检查**：
- 查看 execute_single_step 函数的调试日志
- 确认 step 对象的 params 中包含正确的 elementIndex

## 🔧 修复建议

如果发现问题，可能的修复方向：

1. **前端问题**：检查 JavaScript 中的索引转换逻辑
2. **后端问题**：确保 step 对象的修改能正确传递到执行函数
3. **设备管理器问题**：检查 find_element 方法是否正确使用索引

## ✅ 成功标准

测试成功的标志：
1. 前端正确收集并转换索引值（1→0, 2→1）
2. 后端日志显示"用户指定"而不是"智能索引"
3. 执行时使用正确的索引值
4. 设备上的操作结果符合预期

通过这个测试，我们可以确认元素索引功能是否完全正常工作！
