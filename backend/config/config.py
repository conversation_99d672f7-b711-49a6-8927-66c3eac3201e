import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    PROJECT_NAME = "自动化测试管理系统"
    PROJECT_VERSION = "1.0.0"
    
    # 数据库配置
    POSTGRES_USER = os.getenv("POSTGRES_USER", "postgres")
    POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD", "postgres")
    POSTGRES_SERVER = os.getenv("POSTGRES_SERVER", "localhost")
    POSTGRES_PORT = os.getenv("POSTGRES_PORT", 5432)
    POSTGRES_DB = os.getenv("POSTGRES_DB", "auto_test")
    DATABASE_URL = f"postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_SERVER}:{POSTGRES_PORT}/{POSTGRES_DB}"
    
    # JWT配置
    SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key")
    ALGORITHM = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24  # 24 小时
    
    # 文件存储路径
    UPLOAD_DIR = "uploads"
    SCREENSHOT_DIR = "screenshots"
    LOG_DIR = "logs"
    
    # 邮件配置
    SMTP_SERVER = os.getenv("SMTP_SERVER", "smtp.gmail.com")
    SMTP_PORT = os.getenv("SMTP_PORT", 587)
    SMTP_USER = os.getenv("SMTP_USER", "<EMAIL>")
    SMTP_PASSWORD = os.getenv("SMTP_PASSWORD", "your-password")
    
    # Appium配置
    APPIUM_HOST = os.getenv("APPIUM_HOST", "127.0.0.1")
    APPIUM_PORT = os.getenv("APPIUM_PORT", 4723)

settings = Settings() 