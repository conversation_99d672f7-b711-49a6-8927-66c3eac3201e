from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class ElementBase(BaseModel):
    name: str
    project_id: int
    element_type: str  # id, name, xpath, class_name, css_selector
    element_value: str
    description: Optional[str] = None

class ElementCreate(ElementBase):
    pass

class ElementUpdate(ElementBase):
    name: Optional[str] = None
    project_id: Optional[int] = None
    element_type: Optional[str] = None
    element_value: Optional[str] = None
    description: Optional[str] = None

class ElementResponse(ElementBase):
    id: int
    created_by: int
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        orm_mode = True 