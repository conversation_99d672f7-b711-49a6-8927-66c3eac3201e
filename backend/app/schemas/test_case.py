from pydantic import BaseModel
from typing import Optional, List, Dict
from datetime import datetime

class TestCaseBase(BaseModel):
    name: str
    description: Optional[str] = None
    project_id: int
    steps: List[Dict]  # [{"element_id": 1, "action": "click", "value": null}, ...]

class TestCaseCreate(TestCaseBase):
    pass

class TestCaseUpdate(TestCaseBase):
    name: Optional[str] = None
    description: Optional[str] = None
    project_id: Optional[int] = None
    steps: Optional[List[Dict]] = None

class TestCaseResponse(TestCaseBase):
    id: int
    created_by: int
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        orm_mode = True 