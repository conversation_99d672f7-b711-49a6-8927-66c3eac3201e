from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class TestResultBase(BaseModel):
    test_case_id: int
    status: str  # passed, failed
    error_message: Optional[str] = None

class TestResultCreate(TestResultBase):
    pass

class TestResultResponse(TestResultBase):
    id: int
    screenshot_path: Optional[str]
    log_path: Optional[str]
    execution_time: datetime

    class Config:
        orm_mode = True 