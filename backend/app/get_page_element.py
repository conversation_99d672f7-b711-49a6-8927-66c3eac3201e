import time
import uuid

class PageElementFetcher:
    def __init__(self, device_manager):
        self.device_manager = device_manager

    def get_elements(self, package_name=None, main_activity=None):
        """获取当前页面上的所有元素"""
        # 这里假设 device_manager 有 get_elements 方法
        return self.device_manager.get_elements(package_name, main_activity)

    def capture_element(self, package_name=None, main_activity=None):
        """捕获用户点击的元素"""
        return self.device_manager.capture_element(package_name, main_activity) 