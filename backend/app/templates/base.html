<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动化测试管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            width: 240px;
            background-color: #f8f9fa;
        }
        .main-content {
            margin-left: 240px;
            padding: 20px;
        }
        .nav-link {
            color: #333;
            padding: 0.5rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
        }
        .nav-link.active {
            color: #007bff;
            background-color: #e9ecef;
        }
        .project-submenu {
            padding-left: 1.5rem;
            background-color: #fff;
        }
        .project-submenu .nav-link {
            padding-left: 1.5rem;
            font-size: 0.9rem;
        }
    </style>
    {% block styles %}{% endblock %}
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar">
        <div class="position-sticky">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {% if active_page == 'dashboard' %}active{% endif %}" href="/">
                        <i class="bi bi-speedometer2"></i> 仪表盘
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if active_page == 'projects' %}active{% endif %}" href="/projects">
                        <i class="bi bi-folder"></i> 项目管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if active_page == 'settings' %}active{% endif %}" href="/settings">
                        <i class="bi bi-gear"></i> 系统设置
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if active_page == 'device_elements' %}active{% endif %}" href="/device-elements">
                        <i class="bi bi-phone"></i> 查看设备元素
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>

    <!-- 新建项目模态框 -->
    <div class="modal fade" id="newProjectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">新建项目</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="newProjectForm">
                        <div class="mb-3">
                            <label for="projectName" class="form-label">项目名称</label>
                            <input type="text" class="form-control" id="projectName" required>
                        </div>
                        <div class="mb-3">
                            <label for="projectDescription" class="form-label">项目描述</label>
                            <textarea class="form-control" id="projectDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createProject()">创建</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        async function createProject() {
            const name = document.getElementById('projectName').value;
            const description = document.getElementById('projectDescription').value;
            
            try {
                const response = await fetch('/projects/new', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ name, description }),
                });
                if (response.ok) {
                    window.location.reload();
                } else {
                    const error = await response.json();
                    alert('创建项目失败：' + error.detail);
                }
            } catch (error) {
                alert('catch 创建项目失败：' + error.message);
            }
        }
    </script>
    {% block scripts %}{% endblock %}
</body>
</html> 