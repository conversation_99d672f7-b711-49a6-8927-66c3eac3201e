{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">设备状态</h5>
                </div>
                <div class="card-body">
                    <div id="deviceStatus" class="alert" role="alert">
                        正在检查设备状态...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
async function checkDeviceStatus() {
    try {
        const response = await fetch('/api/devices/status');
        const data = await response.json();
        const statusDiv = document.getElementById('deviceStatus');
        
        if (data.status === 'connected') {
            statusDiv.className = 'alert alert-success';
            statusDiv.textContent = '设备已连接';
        } else {
            statusDiv.className = 'alert alert-danger';
            statusDiv.textContent = '设备未连接: ' + data.error;
        }
    } catch (error) {
        const statusDiv = document.getElementById('deviceStatus');
        statusDiv.className = 'alert alert-danger';
        statusDiv.textContent = '检查设备状态时出错: ' + error.message;
    }
}

// 页面加载完成后检查设备状态
document.addEventListener('DOMContentLoaded', checkDeviceStatus);

// 每30秒刷新一次设备状态
setInterval(checkDeviceStatus, 30000);
</script>
{% endblock %} 