{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>项目管理</h2>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newProjectModal">
            <i class="bi bi-plus"></i> 新建项目
        </button>
    </div>

    <!-- 项目列表 -->
    <div class="row" id="projectsList">
        {% for project in projects %}
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">{{ project.name }}</h5>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-outline-primary" onclick="editProject({{ project.id }})">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteProject({{ project.id }})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <p class="card-text text-muted">{{ project.description or '暂无描述' }}</p>
                    <small class="text-muted d-block mb-3">创建于: {{ project.created_at.strftime('%Y-%m-%d') }}</small>
                    
                    <!-- 测试用例列表 -->
                    <div class="test-cases-section">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0"><i class="bi bi-list-check"></i> 测试用例</h6>
                            <button class="btn btn-sm btn-outline-primary" onclick="showNewTestCaseModal({{ project.id }})">
                                <i class="bi bi-plus-circle"></i> 添加用例
                            </button>
                        </div>
                        {% if project.test_cases %}
                        <div class="list-group">
                            {% for test_case in project.test_cases %}
                            <div class="list-group-item list-group-item-action">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between">
                                            <h6 class="mb-1">{{ test_case.name }}</h6>
                                            <span class="badge bg-secondary test-status-badge" id="status-{{ test_case.id }}">未执行</span>
                                        </div>
                                        <small class="text-muted">{{ test_case.description or '暂无描述' }}</small>
                                        
                                        <!-- 执行结果区域 -->
                                        <div class="execution-result mt-2" id="result-{{ test_case.id }}">
                                            <!-- 进度条 -->
                                            <div class="progress d-none" id="progress-{{ test_case.id }}">
                                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                                            </div>
                                            
                                            <!-- 步骤执行日志 -->
                                            <div class="log-container d-none" id="log-{{ test_case.id }}">
                                                <div class="log-header d-flex justify-content-between align-items-center">
                                                    <span class="text-muted">执行日志</span>
                                                    <button type="button" class="btn btn-sm btn-link p-0" onclick="clearLog({{ test_case.id }})">
                                                        清除
                                                    </button>
                                                </div>
                                                <div class="log-content"></div>
                                            </div>
                                            
                                            <!-- 错误详情 -->
                                            <div class="error-details d-none alert alert-danger mt-2" id="error-{{ test_case.id }}">
                                                <div class="d-flex">
                                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                                    <div class="error-message"></div>
                                                </div>
                                                <div class="error-stack small mt-2 d-none"></div>
                                                <button type="button" class="btn btn-sm btn-outline-danger mt-2" onclick="toggleErrorStack({{ test_case.id }})">
                                                    显示详细信息
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="btn-group ms-3">
                                        <button class="btn btn-sm btn-outline-primary" onclick="executeTestCase({{ test_case.id }}, {{ project.id }})">
                                            <i class="bi bi-play-fill"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editTestCase({{ test_case.id }})">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteTestCase({{ test_case.id }})">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <p class="text-muted small">暂无测试用例</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- 新建项目模态框 -->
<div class="modal fade" id="newProjectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新建项目</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="newProjectForm">
                    <div class="mb-3">
                        <label for="projectName" class="form-label">项目名称</label>
                        <input type="text" class="form-control" id="projectName" required>
                        <div class="invalid-feedback">项目名称不能为空</div>
                    </div>
                    <div class="mb-3">
                        <label for="projectDescription" class="form-label">项目描述</label>
                        <textarea class="form-control" id="projectDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createProject()">创建</button>
            </div>
        </div>
    </div>
</div>

<!-- 新建测试用例模态框 -->
<div class="modal fade" id="newTestCaseModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新建测试用例</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="newTestCaseForm">
                    <input type="hidden" id="testCaseProjectId">
                    <div class="mb-3">
                        <label for="testCaseName" class="form-label">用例名称</label>
                        <input type="text" class="form-control" id="testCaseName" required>
                        <div class="invalid-feedback">用例名称不能为空</div>
                    </div>
                    <div class="mb-3">
                        <label for="testCaseDescription" class="form-label">用例描述</label>
                        <textarea class="form-control" id="testCaseDescription" rows="2"></textarea>
                    </div>
                    
                    <!-- 测试步骤部分 -->
                    <div class="test-steps-section">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">测试步骤</h6>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="addTestStep()">
                                <i class="bi bi-plus"></i> 添加步骤
                            </button>
                        </div>
                        <div id="testStepsList" class="test-steps-list">
                            <!-- 测试步骤将在这里动态添加 -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createTestCase()">创建</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑测试用例模态框 -->
<div class="modal fade" id="editTestCaseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑测试用例</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editTestCaseForm">
                    <input type="hidden" id="editTestCaseId">
                    <div class="mb-3">
                        <label for="editTestCaseName" class="form-label">用例名称</label>
                        <input type="text" class="form-control" id="editTestCaseName" required>
                    </div>
                    <div class="mb-3">
                        <label for="editTestCaseDescription" class="form-label">用例描述</label>
                        <textarea class="form-control" id="editTestCaseDescription" rows="2"></textarea>
                    </div>
                    <!-- 你可以在这里扩展测试步骤的编辑 -->
                    <div class="test-steps-section">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">测试步骤</h6>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="addEditTestStep()">
                                <i class="bi bi-plus"></i> 添加步骤
                            </button>
                        </div>
                        <div id="editTestStepsList" class="test-steps-list">
                            <!-- 编辑时的步骤会动态渲染到这里 -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitEditTestCase()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 测试步骤模板 -->
<template id="testStepTemplate">
    <div class="test-step card mb-3">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-start mb-3">
                <h6 class="step-number mb-0">步骤 #</h6>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeTestStep(this)">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
            <div class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">操作类型</label>
                    <select class="form-select action-type" onchange="handleActionTypeChange(this)">
                        <option value="">请选择操作</option>
                        <option value="click">点击元素</option>
                        <option value="set_text">设置元素文本内容</option>
                        <option value="input">输入文本</option>
                        <option value="scroll">滚动页面</option>
                        <option value="verify">验证内容</option>
                        <option value="wait">等待时间</option>
                    </select>
                </div>
                <div class="col-md-8 action-params">
                    <!-- 操作参数将根据选择的操作类型动态显示 -->
                </div>
            </div>
        </div>
    </div>
</template>
{% endblock %}

{% block styles %}
<style>
.test-cases-section {
    border-top: 1px solid #eee;
    padding-top: 1rem;
    margin-top: 1rem;
}

.list-group-item {
    padding: 0.5rem 1rem;
}

.list-group-item .btn-group {
    opacity: 0.2;
    transition: opacity 0.2s;
}

.list-group-item:hover .btn-group {
    opacity: 1;
}

.test-steps-list {
    max-height: 400px;
    overflow-y: auto;
}

.test-step {
    position: relative;
}

.step-number {
    color: #666;
}

.action-params {
    transition: all 0.3s ease;
}

.test-status-badge {
    font-size: 0.8rem;
    min-width: 60px;
    text-align: center;
}

.progress {
    height: 4px;
    margin-top: 8px;
}

.log-container {
    max-height: 200px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 0.85rem;
    background: #f8f9fa;
    border-radius: 4px;
    margin-top: 8px;
}

/* 元素搜索样式 */
.element-search-container {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-top: 0.5rem;
}

.element-search-input {
    border-color: #ced4da;
    font-size: 0.875rem;
}

.element-search-input:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.clear-search-btn {
    border-color: #ced4da;
}

.clear-search-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

/* 下拉选择样式优化 */
.selector-value option {
    padding: 0.5rem;
    font-size: 0.875rem;
}

.selector-value {
    max-height: 200px;
    overflow-y: auto;
}

/* 当前APP信息样式 */
.current-app-info {
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 0.375rem;
    padding: 0.5rem;
    margin-top: 0.5rem;
}

.current-app-info .bi-phone {
    color: #0066cc;
}

.current-app-info .app-name {
    font-weight: 500;
    color: #0066cc;
}

.log-header {
    padding: 8px;
    border-bottom: 1px solid #dee2e6;
    background: #fff;
    position: sticky;
    top: 0;
    z-index: 1;
}

.log-content {
    padding: 8px;
}

.log-item {
    margin: 2px 0;
    padding: 2px 4px;
    border-radius: 2px;
}

.log-item.success {
    color: #198754;
    background-color: #f8fff9;
}

.log-item.error {
    color: #dc3545;
    background-color: #fff8f8;
}

.log-item.info {
    color: #0d6efd;
    background-color: #f8f9ff;
}

.error-stack {
    font-family: monospace;
    white-space: pre-wrap;
    background: #f8f8f8;
    padding: 8px;
    border-radius: 4px;
}

.execution-result {
    border-top: 1px solid #eee;
    padding-top: 8px;
    margin-top: 8px;
}
</style>
{% endblock %}

{% block scripts %}
<script>
async function createProject() {
    const name = document.getElementById('projectName').value.trim();
    const description = document.getElementById('projectDescription').value.trim();
    
    if (!name) {
        document.getElementById('projectName').classList.add('is-invalid');
        return;
    }
    
    try {
        const response = await fetch('/projects', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ name, description })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            window.location.reload();
        } else {
            alert(result.error || '创建项目失败');
        }
    } catch (error) {
        alert('创建项目失败：' + error);
    }
}

async function deleteProject(projectId) {
    if (!confirm('确定要删除这个项目吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        const response = await fetch(`/projects/${projectId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            window.location.reload();
        } else {
            const result = await response.json();
            alert(result.error || '删除项目失败');
        }
    } catch (error) {
        alert('删除项目失败：' + error);
    }
}

function showNewTestCaseModal(projectId) {
    document.getElementById('testCaseProjectId').value = projectId;
    const modal = new bootstrap.Modal(document.getElementById('newTestCaseModal'));
    modal.show();
}

let stepCounter = 0;

function addTestStep() {
    stepCounter++;
    const template = document.getElementById('testStepTemplate');
    const stepsList = document.getElementById('testStepsList');
    const newStep = template.content.cloneNode(true);
    
    // 更新步骤编号
    newStep.querySelector('.step-number').textContent = `步骤 ${stepCounter}`;
    
    stepsList.appendChild(newStep);
    updateStepNumbers();
}

function removeTestStep(button) {
    const step = button.closest('.test-step');
    step.remove();
    updateStepNumbers();
}

function updateStepNumbers() {
    const steps = document.querySelectorAll('.test-step');
    steps.forEach((step, index) => {
        step.querySelector('.step-number').textContent = `步骤 ${index + 1}`;
    });
    stepCounter = steps.length;
}

function handleActionTypeChange(select) {
    const paramsContainer = select.closest('.row').querySelector('.action-params');
    const actionType = select.value;
    
    let paramsHtml = '';
    switch(actionType) {
        case 'click':
            paramsHtml = `
                <div class="mb-2">
                    <label class="form-label">选择器类型</label>
                    <select class="form-select selector-type">
                        <option value="id">ID</option>
                        <option value="xpath">XPath</option>
                        <option value="text">文本内容</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label class="form-label">选择器值</label>
                    <div class="input-group">
                        <select class="form-select selector-value" style="display: none;">
                            <option value="">请先获取页面元素</option>
                        </select>
                        <input type="text" class="form-control selector-value-input" placeholder="输入选择器值" style="display: block;">
                        <button type="button" class="btn btn-outline-secondary get-elements-btn" onclick="getPageElements(this)">
                            <i class="bi bi-list"></i> 获取元素
                        </button>
                        <button type="button" class="btn btn-outline-secondary check-element-btn" onclick="checkElementCount(this)" style="display: none;">
                            <i class="bi bi-search"></i> 检测
                        </button>
                    </div>
                    <!-- 搜索框 -->
                    <div class="element-search-container mt-2" style="display: none;">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control element-search-input" placeholder="搜索元素（ID、文本、类型）">
                            <button type="button" class="btn btn-outline-secondary clear-search-btn" onclick="clearElementSearch(this)">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                        <small class="text-muted">支持搜索元素ID、文本内容、类型等信息</small>
                    </div>
                    <div class="element-count-info mt-1 d-none">
                        <small class="text-info"></small>
                    </div>
                    <!-- 当前APP信息 -->
                    <div class="current-app-info mt-1 d-none">
                        <small class="text-muted">
                            <i class="bi bi-phone"></i> 当前APP: <span class="app-name"></span>
                        </small>
                    </div>
                </div>
                <div class="mb-2">
                    <label class="form-label">第几个元素</label>
                    <input type="number" class="form-control element-index" placeholder="1" min="1" value="1">
                    <small class="form-text text-muted">默认第1个元素（如果有多个相同ID的元素）</small>
                </div>
            `;
            break;
        case 'set_text':
            paramsHtml = `
                <div class="mb-2">
                    <label class="form-label">选择器类型</label>
                    <select class="form-select selector-type">
                        <option value="id">ID</option>
                        <option value="xpath">XPath</option>
                        <option value="text">文本内容</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label class="form-label">选择器值</label>
                    <div class="input-group">
                        <select class="form-select selector-value" style="display: none;">
                            <option value="">请先获取页面元素</option>
                        </select>
                        <input type="text" class="form-control selector-value-input" placeholder="输入选择器值" style="display: block;">
                        <button type="button" class="btn btn-outline-secondary get-elements-btn" onclick="getPageElements(this)">
                            <i class="bi bi-list"></i> 获取元素
                        </button>
                        <button type="button" class="btn btn-outline-secondary check-element-btn" onclick="checkElementCount(this)" style="display: none;">
                            <i class="bi bi-search"></i> 检测
                        </button>
                    </div>
                    <!-- 搜索框 -->
                    <div class="element-search-container mt-2" style="display: none;">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control element-search-input" placeholder="搜索元素（ID、文本、类型）">
                            <button type="button" class="btn btn-outline-secondary clear-search-btn" onclick="clearElementSearch(this)">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                        <small class="text-muted">支持搜索元素ID、文本内容、类型等信息</small>
                    </div>
                    <div class="element-count-info mt-1 d-none">
                        <small class="text-info"></small>
                    </div>
                    <!-- 当前APP信息 -->
                    <div class="current-app-info mt-1 d-none">
                        <small class="text-muted">
                            <i class="bi bi-phone"></i> 当前APP: <span class="app-name"></span>
                        </small>
                    </div>
                </div>
                <div class="mb-2">
                    <label class="form-label">第几个元素</label>
                    <input type="number" class="form-control element-index" placeholder="1" min="1" value="1">
                    <small class="form-text text-muted">默认第1个元素（如果有多个相同ID的元素）</small>
                </div>
                <div class="mb-2">
                    <label class="form-label">要设置的文本内容</label>
                    <input type="text" class="form-control input-value" placeholder="输入要设置的内容">
                </div>
            `;
            break;
            
        case 'input':
            paramsHtml = `
                <div class="mb-2">
                    <label class="form-label">选择器类型</label>
                    <select class="form-select selector-type">
                        <option value="id">ID</option>
                        <option value="xpath">XPath</option>
                        <option value="text">文本内容</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label class="form-label">选择器值</label>
                    <div class="input-group">
                        <select class="form-select selector-value" style="display: none;">
                            <option value="">请先获取页面元素</option>
                        </select>
                        <input type="text" class="form-control selector-value-input" placeholder="输入选择器值" style="display: block;">
                        <button type="button" class="btn btn-outline-secondary get-elements-btn" onclick="getPageElements(this)">
                            <i class="bi bi-list"></i> 获取元素
                        </button>
                        <button type="button" class="btn btn-outline-secondary check-element-btn" onclick="checkElementCount(this)" style="display: none;">
                            <i class="bi bi-search"></i> 检测
                        </button>
                    </div>
                    <!-- 搜索框 -->
                    <div class="element-search-container mt-2" style="display: none;">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control element-search-input" placeholder="搜索元素（ID、文本、类型）">
                            <button type="button" class="btn btn-outline-secondary clear-search-btn" onclick="clearElementSearch(this)">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                        <small class="text-muted">支持搜索元素ID、文本内容、类型等信息</small>
                    </div>
                    <div class="element-count-info mt-1 d-none">
                        <small class="text-info"></small>
                    </div>
                    <!-- 当前APP信息 -->
                    <div class="current-app-info mt-1 d-none">
                        <small class="text-muted">
                            <i class="bi bi-phone"></i> 当前APP: <span class="app-name"></span>
                        </small>
                    </div>
                </div>
                <div class="mb-2">
                    <label class="form-label">第几个元素</label>
                    <input type="number" class="form-control element-index" placeholder="1" min="1" value="1">
                    <small class="form-text text-muted">默认第1个元素（如果有多个相同ID的元素）</small>
                </div>
                <div class="mb-2">
                    <label class="form-label">输入内容</label>
                    <input type="text" class="form-control input-value" placeholder="输入要填写的内容">
                </div>
            `;
            break;
            
        case 'scroll':
            paramsHtml = `
                <div class="mb-2">
                    <label class="form-label">滚动方向</label>
                    <select class="form-select scroll-direction">
                        <option value="up">向上</option>
                        <option value="down">向下</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label class="form-label">滚动距离（像素）</label>
                    <input type="number" class="form-control scroll-distance" value="100">
                </div>
            `;
            break;
            
        case 'verify':
            paramsHtml = `
                <div class="mb-2">
                    <label class="form-label">验证类型</label>
                    <select class="form-select verify-type">
                        <option value="text">文本内容</option>
                        <option value="visible">元素可见性</option>
                        <option value="enabled">元素可用性</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label class="form-label">选择器类型</label>
                    <select class="form-select selector-type">
                        <option value="id">ID</option>
                        <option value="xpath">XPath</option>
                        <option value="text">文本内容</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label class="form-label">选择器值</label>
                    <div class="input-group">
                        <select class="form-select selector-value" style="display: none;">
                            <option value="">请先获取页面元素</option>
                        </select>
                        <input type="text" class="form-control selector-value-input" placeholder="输入选择器值" style="display: block;">
                        <button type="button" class="btn btn-outline-secondary get-elements-btn" onclick="getPageElements(this)">
                            <i class="bi bi-list"></i> 获取元素
                        </button>
                        <button type="button" class="btn btn-outline-secondary check-element-btn" onclick="checkElementCount(this)" style="display: none;">
                            <i class="bi bi-search"></i> 检测
                        </button>
                    </div>
                    <!-- 搜索框 -->
                    <div class="element-search-container mt-2" style="display: none;">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control element-search-input" placeholder="搜索元素（ID、文本、类型）">
                            <button type="button" class="btn btn-outline-secondary clear-search-btn" onclick="clearElementSearch(this)">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                        <small class="text-muted">支持搜索元素ID、文本内容、类型等信息</small>
                    </div>
                    <div class="element-count-info mt-1 d-none">
                        <small class="text-info"></small>
                    </div>
                    <!-- 当前APP信息 -->
                    <div class="current-app-info mt-1 d-none">
                        <small class="text-muted">
                            <i class="bi bi-phone"></i> 当前APP: <span class="app-name"></span>
                        </small>
                    </div>
                </div>
                <div class="mb-2">
                    <label class="form-label">第几个元素</label>
                    <input type="number" class="form-control element-index" placeholder="1" min="1" value="1">
                    <small class="form-text text-muted">默认第1个元素（如果有多个相同ID的元素）</small>
                </div>
                <div class="mb-2 expected-value-container">
                    <label class="form-label">期望值</label>
                    <input type="text" class="form-control expected-value" placeholder="输入期望值">
                </div>
            `;
            break;
            
        case 'wait':
            paramsHtml = `
                <div class="mb-2">
                    <label class="form-label">等待时间（秒）</label>
                    <input type="number" class="form-control wait-seconds" value="1" min="1" max="60">
                </div>
            `;
            break;
    }
    
    paramsContainer.innerHTML = paramsHtml;
}

async function createTestCase() {
    const name = document.getElementById('testCaseName').value.trim();
    const description = document.getElementById('testCaseDescription').value.trim();
    const projectId = document.getElementById('testCaseProjectId').value;
    
    if (!name) {
        document.getElementById('testCaseName').classList.add('is-invalid');
        return;
    }
    
    // 收集所有测试步骤
    const steps = [];
    document.querySelectorAll('.test-step').forEach((stepElement, index) => {
        const actionType = stepElement.querySelector('.action-type').value;
        if (!actionType) return;
        
        const step = {
            order: index + 1,
            type: actionType,
            params: {}
        };
        
        switch(actionType) {
            case 'click':
            case 'input':
            case 'set_text':
                step.params.selectorType = stepElement.querySelector('.selector-type') ? stepElement.querySelector('.selector-type').value : '';
                // 优先使用下拉选择的值，如果没有则使用输入框的值
                const selectorValueSelect = stepElement.querySelector('.selector-value');
                const selectorValueInput = stepElement.querySelector('.selector-value-input');
                step.params.selectorValue = (selectorValueSelect && selectorValueSelect.style.display !== 'none' && selectorValueSelect.value)
                    ? selectorValueSelect.value
                    : (selectorValueInput ? selectorValueInput.value : '');
                step.params.inputValue = stepElement.querySelector('.input-value') ? stepElement.querySelector('.input-value').value : '';
                step.params.elementIndex = stepElement.querySelector('.element-index') ? parseInt(stepElement.querySelector('.element-index').value) - 1 : 0; // 转换为0基索引
                break;
                
            case 'scroll':
                step.params.direction = stepElement.querySelector('.scroll-direction').value;
                step.params.distance = stepElement.querySelector('.scroll-distance').value;
                break;
                
            case 'verify':
                step.params.verifyType = stepElement.querySelector('.verify-type').value;
                step.params.selectorType = stepElement.querySelector('.selector-type').value;
                // 优先使用下拉选择的值，如果没有则使用输入框的值
                const verifySelectorValueSelect = stepElement.querySelector('.selector-value');
                const verifySelectorValueInput = stepElement.querySelector('.selector-value-input');
                step.params.selectorValue = (verifySelectorValueSelect && verifySelectorValueSelect.style.display !== 'none' && verifySelectorValueSelect.value)
                    ? verifySelectorValueSelect.value
                    : (verifySelectorValueInput ? verifySelectorValueInput.value : '');
                step.params.expectedValue = stepElement.querySelector('.expected-value').value;
                step.params.elementIndex = stepElement.querySelector('.element-index') ? parseInt(stepElement.querySelector('.element-index').value) - 1 : 0; // 转换为0基索引
                break;
                
            case 'wait':
                step.params.seconds = stepElement.querySelector('.wait-seconds').value;
                break;
        }
        
        steps.push(step);
    });
    
    try {
        const response = await fetch(`/projects/${projectId}/testcases`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name,
                description,
                steps
            })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            window.location.reload();
        } else {
            alert(result.error || '创建测试用例失败');
        }
    } catch (error) {
        alert('创建测试用例失败：' + error);
    }
}

async function deleteTestCase(testCaseId) {
    if (!confirm('确定要删除这个测试用例吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        const response = await fetch(`/testcases/${testCaseId}/delete`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            window.location.reload();
        } else {
            const result = await response.json();
            alert(result.error || '删除测试用例失败');
        }
    } catch (error) {
        alert('删除测试用例失败：' + error);
    }
}

let testCaseEventSources = {};

async function executeTestCase(testCaseId, projectId) {
    const statusBadge = document.getElementById(`status-${testCaseId}`);
    const progressBar = document.getElementById(`progress-${testCaseId}`);
    const logContainer = document.getElementById(`log-${testCaseId}`);
    const errorDetails = document.getElementById(`error-${testCaseId}`);

    try {
        // 重置UI状态
        statusBadge.className = 'badge bg-primary test-status-badge';
        statusBadge.textContent = '执行中';
        progressBar.classList.remove('d-none');
        logContainer.classList.remove('d-none');
        logContainer.querySelector('.log-content').innerHTML = '';
        errorDetails.classList.add('d-none');

        // 关闭之前的事件源（如果存在）
        if (testCaseEventSources[testCaseId]) {
            testCaseEventSources[testCaseId].close();
        }

        // 先发送执行请求
        const response = await fetch(`/testcases/${testCaseId}/execute`, {
            method: 'POST'
        });
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || '执行失败');
        }

        // 再连接到执行状态事件流
        const eventSource = new EventSource(`/testcases/${testCaseId}/execute?project_id=${projectId}`);
        testCaseEventSources[testCaseId] = eventSource;

        eventSource.onmessage = function(event) {
            const data = JSON.parse(event.data);
            if (data.type === 'progress') {
                progressBar.querySelector('.progress-bar').style.width = `${data.progress}%`;
                if (data.status) {
                    statusBadge.textContent = data.status;
                }
                if (data.message) {
                    addExecutionLog(testCaseId, data.message, data.level || 'info');
                }
                if (data.error) {
                    showErrorDetails(testCaseId, data.error, data.stack);
                }
            } else if (data.type === 'complete') {
                statusBadge.className = `badge ${data.success ? 'bg-success' : 'bg-danger'} test-status-badge`;
                statusBadge.textContent = data.success ? '成功' : '失败';
                setTimeout(() => {
                    progressBar.classList.add('d-none');
                    progressBar.querySelector('.progress-bar').style.width = '0%';
                }, 1000);
                eventSource.close();
                delete testCaseEventSources[testCaseId];
            }
        };

        eventSource.onerror = function(error) {
            console.error('执行状态连接错误:', error);
            statusBadge.className = 'badge bg-danger test-status-badge';
            statusBadge.textContent = '错误';
            progressBar.classList.add('d-none');
            showErrorDetails(testCaseId, '执行状态连接失败');
            eventSource.close();
            delete testCaseEventSources[testCaseId];
        };
    } catch (error) {
        console.error('执行测试用例失败:', error);
        statusBadge.className = 'badge bg-danger test-status-badge';
        statusBadge.textContent = '错误';
        progressBar.classList.add('d-none');
        showErrorDetails(testCaseId, error.message);
    }
}

function addExecutionLog(testCaseId, message, level = 'info') {
    const logContent = document.querySelector(`#log-${testCaseId} .log-content`);
    if (!logContent) return;
    
    const logItem = document.createElement('div');
    logItem.className = `log-item ${level}`;
    
    const time = new Date().toLocaleTimeString();
    logItem.innerHTML = `<span class="text-muted">[${time}]</span> ${message}`;
    
    logContent.appendChild(logItem);
    logContent.scrollTop = logContent.scrollHeight;
}

function showErrorDetails(testCaseId, message, stack = null) {
    const errorDetails = document.getElementById(`error-${testCaseId}`);
    if (!errorDetails) return;
    
    errorDetails.classList.remove('d-none');
    errorDetails.querySelector('.error-message').textContent = message;
    
    const stackElement = errorDetails.querySelector('.error-stack');
    if (stack) {
        stackElement.textContent = stack;
        errorDetails.querySelector('button').classList.remove('d-none');
    } else {
        stackElement.classList.add('d-none');
        errorDetails.querySelector('button').classList.add('d-none');
    }
}

function toggleErrorStack(testCaseId) {
    const stackElement = document.querySelector(`#error-${testCaseId} .error-stack`);
    const button = document.querySelector(`#error-${testCaseId} button`);
    
    if (stackElement.classList.contains('d-none')) {
        stackElement.classList.remove('d-none');
        button.textContent = '隐藏详细信息';
    } else {
        stackElement.classList.add('d-none');
        button.textContent = '显示详细信息';
    }
}

function clearLog(testCaseId) {
    const logContent = document.querySelector(`#log-${testCaseId} .log-content`);
    if (logContent) {
        logContent.innerHTML = '';
    }
    // 隐藏错误信息
    const errorDetails = document.getElementById(`error-${testCaseId}`);
    if (errorDetails) {
        errorDetails.classList.add('d-none');
    }
}

// 页面卸载时清理所有事件源
window.addEventListener('beforeunload', function() {
    Object.values(testCaseEventSources).forEach(eventSource => {
        if (eventSource) {
            eventSource.close();
        }
    });
});

// 监听输入，移除错误提示
document.getElementById('projectName').addEventListener('input', function(e) {
    e.target.classList.remove('is-invalid');
});

document.getElementById('testCaseName').addEventListener('input', function(e) {
    e.target.classList.remove('is-invalid');
});

// 编辑用例时渲染步骤
function renderEditTestSteps(steps) {
    const stepsList = document.getElementById('editTestStepsList');
    stepsList.innerHTML = '';
    (steps || []).forEach((step, idx) => {
        const template = document.getElementById('testStepTemplate');
        const newStep = template.content.cloneNode(true);
        newStep.querySelector('.step-number').textContent = `步骤 ${idx + 1}`;
        const actionTypeSelect = newStep.querySelector('.action-type');
        actionTypeSelect.value = step.type;
        handleActionTypeChange(actionTypeSelect);
        fillStepParams(newStep, step);
        newStep.querySelector('.btn-outline-danger').onclick = function() {
            newStep.remove();
            updateEditStepNumbers();
        };
        actionTypeSelect.onchange = function() {
            handleActionTypeChange(actionTypeSelect);
        };
        stepsList.appendChild(newStep);
    });
    updateEditStepNumbers();
}

function fillStepParams(stepElement, step) {
    switch(step.type) {
        case 'click':
        case 'input':
        case 'set_text':
            if (stepElement.querySelector('.selector-type')) stepElement.querySelector('.selector-type').value = step.params.selectorType || '';
            if (stepElement.querySelector('.selector-value')) stepElement.querySelector('.selector-value').value = step.params.selectorValue || '';
            if (stepElement.querySelector('.element-index')) stepElement.querySelector('.element-index').value = (step.params.elementIndex || 0) + 1; // 转换为1基索引显示
            if (step.type === 'input' || step.type === 'set_text') {
                if (stepElement.querySelector('.input-value')) stepElement.querySelector('.input-value').value = step.params.inputValue || '';
            }
            break;
        case 'scroll':
            stepElement.querySelector('.scroll-direction').value = step.params.direction || '';
            stepElement.querySelector('.scroll-distance').value = step.params.distance || '';
            break;
        case 'verify':
            stepElement.querySelector('.verify-type').value = step.params.verifyType || '';
            stepElement.querySelector('.selector-type').value = step.params.selectorType || '';
            stepElement.querySelector('.selector-value').value = step.params.selectorValue || '';
            stepElement.querySelector('.expected-value').value = step.params.expectedValue || '';
            if (stepElement.querySelector('.element-index')) stepElement.querySelector('.element-index').value = (step.params.elementIndex || 0) + 1; // 转换为1基索引显示
            break;
        case 'wait':
            stepElement.querySelector('.wait-seconds').value = step.params.seconds || '';
            break;
    }
}

function addEditTestStep() {
    const template = document.getElementById('testStepTemplate');
    const stepsList = document.getElementById('editTestStepsList');
    const newStep = template.content.cloneNode(true);
    newStep.querySelector('.step-number').textContent = `步骤 ${stepsList.children.length + 1}`;
    // 绑定删除
    newStep.querySelector('.btn-outline-danger').onclick = function() {
        newStep.remove();
        updateEditStepNumbers();
    };
    // 绑定操作类型切换
    const actionTypeSelect = newStep.querySelector('.action-type');
    actionTypeSelect.onchange = function() {
        handleActionTypeChange(actionTypeSelect);
    };
    // 默认触发一次，渲染参数区域
    handleActionTypeChange(actionTypeSelect);
    stepsList.appendChild(newStep);
    updateEditStepNumbers();
}

function updateEditStepNumbers() {
    const steps = document.querySelectorAll('#editTestStepsList .test-step');
    steps.forEach((step, idx) => {
        step.querySelector('.step-number').textContent = `步骤 ${idx + 1}`;
    });
}

// 修改editTestCase函数，渲染步骤
async function editTestCase(testCaseId) {
    try {
        const response = await fetch(`/testcases/${testCaseId}`);
        if (!response.ok) {
            alert('获取用例信息失败');
            return;
        }
        const data = await response.json();
        document.getElementById('editTestCaseId').value = data.id;
        document.getElementById('editTestCaseName').value = data.name;
        document.getElementById('editTestCaseDescription').value = data.description || '';
        renderEditTestSteps(data.steps || []);
        const modal = new bootstrap.Modal(document.getElementById('editTestCaseModal'));
        modal.show();
    } catch (err) {
        alert('获取用例信息失败: ' + err);
    }
}

// 修改submitEditTestCase函数，收集步骤
async function submitEditTestCase() {
    const id = document.getElementById('editTestCaseId').value;
    const name = document.getElementById('editTestCaseName').value.trim();
    const description = document.getElementById('editTestCaseDescription').value.trim();
    // 收集所有测试步骤
    const steps = [];
    document.querySelectorAll('#editTestStepsList .test-step').forEach((stepElement, index) => {
        const actionType = stepElement.querySelector('.action-type').value;
        if (!actionType) return;
        const step = {
            order: index + 1,
            type: actionType,
            params: {}
        };
        switch(actionType) {
            case 'click':
            case 'input':
            case 'set_text':
                step.params.selectorType = stepElement.querySelector('.selector-type') ? stepElement.querySelector('.selector-type').value : '';
                // 优先使用下拉选择的值，如果没有则使用输入框的值
                const editSelectorValueSelect = stepElement.querySelector('.selector-value');
                const editSelectorValueInput = stepElement.querySelector('.selector-value-input');
                step.params.selectorValue = (editSelectorValueSelect && editSelectorValueSelect.style.display !== 'none' && editSelectorValueSelect.value)
                    ? editSelectorValueSelect.value
                    : (editSelectorValueInput ? editSelectorValueInput.value : '');
                step.params.inputValue = stepElement.querySelector('.input-value') ? stepElement.querySelector('.input-value').value : '';
                step.params.elementIndex = stepElement.querySelector('.element-index') ? parseInt(stepElement.querySelector('.element-index').value) - 1 : 0; // 转换为0基索引
                break;
            case 'scroll':
                step.params.direction = stepElement.querySelector('.scroll-direction').value;
                step.params.distance = stepElement.querySelector('.scroll-distance').value;
                break;
            case 'verify':
                step.params.verifyType = stepElement.querySelector('.verify-type').value;
                step.params.selectorType = stepElement.querySelector('.selector-type').value;
                // 优先使用下拉选择的值，如果没有则使用输入框的值
                const editVerifySelectorValueSelect = stepElement.querySelector('.selector-value');
                const editVerifySelectorValueInput = stepElement.querySelector('.selector-value-input');
                step.params.selectorValue = (editVerifySelectorValueSelect && editVerifySelectorValueSelect.style.display !== 'none' && editVerifySelectorValueSelect.value)
                    ? editVerifySelectorValueSelect.value
                    : (editVerifySelectorValueInput ? editVerifySelectorValueInput.value : '');
                step.params.expectedValue = stepElement.querySelector('.expected-value').value;
                step.params.elementIndex = stepElement.querySelector('.element-index') ? parseInt(stepElement.querySelector('.element-index').value) - 1 : 0; // 转换为0基索引
                break;
            case 'wait':
                step.params.seconds = stepElement.querySelector('.wait-seconds').value;
                break;
        }
        steps.push(step);
    });
    if (!name) {
        alert('用例名称不能为空');
        return;
    }
    try {
        const response = await fetch(`/testcases/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name, description, steps })
        });
        const result = await response.json();
        if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('editTestCaseModal')).hide();
            window.location.reload();
        } else {
            alert(result.error || '保存失败');
        }
    } catch (err) {
        alert('保存失败: ' + err);
    }
}

// 获取页面元素列表的函数
async function getPageElements(button) {
    const inputGroup = button.closest('.input-group');
    const selectElement = inputGroup.querySelector('.selector-value');
    const inputElement = inputGroup.querySelector('.selector-value-input');
    const checkButton = inputGroup.querySelector('.check-element-btn');
    const searchContainer = inputGroup.parentElement.querySelector('.element-search-container');
    const searchInput = searchContainer.querySelector('.element-search-input');
    const infoDiv = inputGroup.parentElement.querySelector('.element-count-info');
    const infoText = infoDiv.querySelector('small');
    const appInfoDiv = inputGroup.parentElement.querySelector('.current-app-info');
    const appNameSpan = appInfoDiv.querySelector('.app-name');

    try {
        button.disabled = true;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i> 获取中...';

        const response = await fetch('/api/get-page-elements');
        const result = await response.json();

        if (result.success && result.elements.length > 0) {
            // 存储所有元素数据到select元素上，用于搜索
            selectElement.allElements = result.elements;

            // 初始化显示所有元素
            populateElementOptions(selectElement, result.elements);

            // 切换显示模式
            inputElement.style.display = 'none';
            selectElement.style.display = 'block';
            button.style.display = 'none';
            checkButton.style.display = 'block';
            searchContainer.style.display = 'block';

            // 绑定选择事件
            selectElement.onchange = function() {
                const selectedOption = this.options[this.selectedIndex];
                if (selectedOption.dataset.elementIndex !== undefined) {
                    // 自动设置元素索引
                    const elementIndexInput = inputGroup.parentElement.querySelector('.element-index');
                    if (elementIndexInput) {
                        elementIndexInput.value = parseInt(selectedOption.dataset.elementIndex) + 1; // 转换为1基索引
                    }
                }
            };

            // 绑定搜索事件
            searchInput.oninput = function() {
                filterElements(selectElement, this.value);
            };

            infoText.textContent = `找到 ${result.elements.length} 个当前APP元素`;
            infoText.className = 'text-success';
            infoDiv.classList.remove('d-none');

            // 显示当前APP信息
            if (result.app_info) {
                appNameSpan.textContent = `${result.app_info.name} (${result.app_info.package})`;
                appInfoDiv.classList.remove('d-none');
            }

        } else {
            infoText.textContent = '未找到任何元素';
            infoText.className = 'text-warning';
            infoDiv.classList.remove('d-none');

            // 仍然显示APP信息
            if (result.app_info) {
                appNameSpan.textContent = `${result.app_info.name} (${result.app_info.package})`;
                appInfoDiv.classList.remove('d-none');
            }
        }

    } catch (error) {
        infoText.textContent = '获取元素失败：' + error.message;
        infoText.className = 'text-danger';
        infoDiv.classList.remove('d-none');
    } finally {
        button.disabled = false;
        button.innerHTML = '<i class="bi bi-list"></i> 获取元素';
    }
}

// 填充元素选项
function populateElementOptions(selectElement, elements) {
    // 清空并填充下拉选项
    selectElement.innerHTML = '<option value="">请选择元素</option>';

    // 按resource-id分组
    const groupedElements = {};
    elements.forEach(element => {
        if (element.resource_id) {
            if (!groupedElements[element.resource_id]) {
                groupedElements[element.resource_id] = [];
            }
            groupedElements[element.resource_id].push(element);
        }
    });

    // 添加选项
    Object.keys(groupedElements).sort().forEach(resourceId => {
        const elements = groupedElements[resourceId];
        elements.forEach((element, index) => {
            const option = document.createElement('option');
            option.value = element.resource_id;
            option.textContent = element.display_text;
            option.dataset.elementIndex = element.index;
            option.dataset.searchText = element.display_text.toLowerCase(); // 用于搜索
            selectElement.appendChild(option);
        });
    });
}

// 过滤元素
function filterElements(selectElement, searchTerm) {
    if (!selectElement.allElements) return;

    const filteredElements = selectElement.allElements.filter(element => {
        const searchText = searchTerm.toLowerCase();
        return element.resource_id.toLowerCase().includes(searchText) ||
               (element.text && element.text.toLowerCase().includes(searchText)) ||
               (element.content_desc && element.content_desc.toLowerCase().includes(searchText)) ||
               (element.class_name && element.class_name.toLowerCase().includes(searchText)) ||
               element.display_text.toLowerCase().includes(searchText);
    });

    populateElementOptions(selectElement, filteredElements);
}

// 清除搜索
function clearElementSearch(button) {
    const searchContainer = button.closest('.element-search-container');
    const searchInput = searchContainer.querySelector('.element-search-input');
    const selectElement = searchContainer.parentElement.querySelector('.selector-value');

    searchInput.value = '';
    if (selectElement.allElements) {
        populateElementOptions(selectElement, selectElement.allElements);
    }
}

// 检测元素数量的函数
async function checkElementCount(button) {
    const stepContainer = button.closest('.test-step');
    const selectorTypeSelect = stepContainer.querySelector('.selector-type');
    const selectorValueInput = stepContainer.querySelector('.selector-value');
    const countInfoDiv = stepContainer.querySelector('.element-count-info');
    const countInfoText = countInfoDiv.querySelector('small');

    const selectorType = selectorTypeSelect.value;
    const selectorValue = selectorValueInput.value.trim();

    if (!selectorValue) {
        alert('请先输入选择器值');
        return;
    }

    // 只对ID类型的选择器进行检测
    if (selectorType !== 'id') {
        countInfoDiv.classList.add('d-none');
        return;
    }

    // 显示加载状态
    button.disabled = true;
    button.innerHTML = '<i class="bi bi-hourglass-split"></i> 检测中...';
    countInfoDiv.classList.remove('d-none');
    countInfoText.textContent = '正在检测元素数量...';
    countInfoText.className = 'text-info';

    try {
        const response = await fetch('/api/check-element-count', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                resourceId: selectorValue
            })
        });

        const result = await response.json();

        if (result.success) {
            const count = result.count;

            if (count > 1) {
                // 发现多个相同ID的元素，提示用户选择索引
                countInfoText.innerHTML = `
                    <i class="bi bi-exclamation-triangle text-warning"></i>
                    找到 <strong>${count}</strong> 个相同ID的元素！
                    <br>建议选择 "ID + 索引" 类型来指定第几个元素
                `;
                countInfoText.className = 'text-warning';

                // 自动切换到 "ID + 索引" 选择器类型
                if (selectorTypeSelect.querySelector('option[value="id_index"]')) {
                    selectorTypeSelect.value = 'id_index';
                    handleSelectorTypeChange(selectorTypeSelect);
                }
            } else if (count === 1) {
                countInfoText.innerHTML = `
                    <i class="bi bi-check-circle text-success"></i>
                    找到 1 个匹配的元素
                `;
                countInfoText.className = 'text-success';
            } else {
                countInfoText.innerHTML = `
                    <i class="bi bi-x-circle text-danger"></i>
                    未找到匹配的元素，请检查ID是否正确
                `;
                countInfoText.className = 'text-danger';
            }
        } else {
            countInfoText.innerHTML = `
                <i class="bi bi-x-circle text-danger"></i>
                检测失败: ${result.error}
            `;
            countInfoText.className = 'text-danger';
        }
    } catch (error) {
        countInfoText.innerHTML = `
            <i class="bi bi-x-circle text-danger"></i>
            检测失败: ${error.message}
        `;
        countInfoText.className = 'text-danger';
    } finally {
        // 恢复按钮状态
        button.disabled = false;
        button.innerHTML = '<i class="bi bi-search"></i> 检测';
    }
}
</script>
{% endblock %}