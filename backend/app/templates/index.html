{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">总项目数</h5>
                    <h2 class="card-text">{{ projects|length }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">总测试用例</h5>
                    <h2 class="card-text">{{ test_cases|length }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">总测试结果</h5>
                    <h2 class="card-text">{{ test_results|length }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">成功率</h5>
                    <h2 class="card-text">
                        {% set success_count = test_results|selectattr("status", "equalto", "success")|list|length %}
                        {% if test_results|length > 0 %}
                            {{ "%.1f"|format(success_count / test_results|length * 100) }}%
                        {% else %}
                            0%
                        {% endif %}
                    </h2>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目列表 -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">项目列表</h5>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newProjectModal">
                        <i class="bi bi-plus-lg"></i> 新建项目
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>项目名称</th>
                                    <th>测试用例数</th>
                                    <th>成功数</th>
                                    <th>失败数</th>
                                    <th>成功率</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for project in projects %}
                                <tr>
                                    <td>
                                        <a href="/projects/{{ project.id }}" class="text-decoration-none">
                                            {{ project.name }}
                                        </a>
                                    </td>
                                    <td>{{ project_stats[project.id].total_cases }}</td>
                                    <td class="text-success">{{ project_stats[project.id].success_count }}</td>
                                    <td class="text-danger">{{ project_stats[project.id].failure_count }}</td>
                                    <td>
                                        {% if project_stats[project.id].total_results > 0 %}
                                            {{ "%.1f"|format(project_stats[project.id].success_count / project_stats[project.id].total_results * 100) }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="/projects/{{ project.id }}" class="btn btn-sm btn-primary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <button class="btn btn-sm btn-danger" onclick="deleteProject('{{ project.id }}')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近测试结果 -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">最近测试结果</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>项目</th>
                                    <th>测试用例</th>
                                    <th>状态</th>
                                    <th>执行时间</th>
                                    <th>错误信息</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for result in recent_results %}
                                <tr>
                                    <td>
                                        <a href="/projects/{{ result.project_id }}" class="text-decoration-none">
                                            {{ result.project_name }}
                                        </a>
                                    </td>
                                    <td>{{ result.test_case_name }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if result.status == 'success' else 'danger' }}">
                                            {{ result.status }}
                                        </span>
                                    </td>
                                    <td>{{ "%.2f"|format(result.execution_time) }}s</td>
                                    <td>{{ result.error_message or '' }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-info" onclick="viewTestResult('{{ result.id }}')">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    async function deleteProject(projectId) {
        if (!confirm('确定要删除这个项目吗？这将同时删除项目下的所有测试用例和测试结果！')) {
            return;
        }
        
        try {
            const response = await fetch(`/projects/${projectId}`, {
                method: 'DELETE',
            });
            
            if (response.ok) {
                window.location.reload();
            } else {
                const error = await response.json();
                alert('删除项目失败：' + error.detail);
            }
        } catch (error) {
            alert('删除项目失败：' + error.message);
        }
    }

    function viewTestResult(resultId) {
        // TODO: 实现查看测试结果详情功能
        alert('查看测试结果详情功能待实现');
    }
</script>
{% endblock %} 