{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <h2>设备元素检查</h2>
    
    <!-- 包名和Activity设置 -->
    <div class="card mb-4">
        <div class="card-header">
            应用设置
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="packageSelect" class="form-label">选择应用包名：</label>
                    <select class="form-select" id="packageSelect">
                        <option value="">请选择包名</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="mainActivity" class="form-label">主Activity：</label>
                    <input type="text" class="form-control" id="mainActivity" placeholder="请输入主Activity（可选）">
                    <div class="form-text">如不填写，系统将尝试自动查找</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作按钮和过滤器 -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-end">
                <div class="col-md-4">
                    <button class="btn btn-primary" onclick="getElements()">获取元素</button>
                </div>
                <div class="col-md-4">
                    <label for="resourceFilter" class="form-label">资源ID过滤：</label>
                    <select class="form-select" id="resourceFilter" onchange="applyFilters()">
                        <option value="all">显示全部</option>
                        <option value="hasResource">有资源ID</option>
                        <option value="noResource">无资源ID</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="textFilter" class="form-label">文本过滤：</label>
                    <select class="form-select" id="textFilter" onchange="applyFilters()">
                        <option value="all">显示全部</option>
                        <option value="hasText">有文本</option>
                        <option value="noText">无文本</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 进度日志 -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>进度日志</span>
                    <button class="btn btn-sm btn-outline-secondary" onclick="clearLogs()">清除</button>
                </div>
                <div class="card-body">
                    <div id="progressLogs" class="progress-logs">
                        <!-- 进度日志将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 元素列表 -->
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>元素列表</span>
                    <span class="badge bg-primary" id="elementCount">0 个元素</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>类型</th>
                                    <th>文本</th>
                                    <th>资源ID</th>
                                    <th>包名</th>
                                    <th>XPath</th>
                                    <th>可点击</th>
                                </tr>
                            </thead>
                            <tbody id="elementsTable">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 进度弹窗 -->
<div class="modal fade" id="progressModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">操作进度</h5>
            </div>
            <div class="modal-body">
                <div class="progress mb-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>
                <p id="progressMessage" class="text-center mb-0"></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
.progress-logs {
    height: 400px;
    overflow-y: auto;
    font-size: 0.9em;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.progress-log-item {
    padding: 8px;
    border-bottom: 1px solid #eee;
    background-color: white;
    margin-bottom: 4px;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.progress-log-item.element-progress {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.progress-log-item.element-details {
    background-color: #f3e5f5;
    border-left: 3px solid #9c27b0;
    margin-left: 20px;
    font-size: 0.9em;
}

.progress-log-item.general-progress {
    border-left: 3px solid #4caf50;
}

.progress-log-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.progress-log-time {
    color: #666;
    font-size: 0.8em;
    margin-bottom: 4px;
}

.progress-log-message {
    margin-top: 2px;
    color: #333;
    line-height: 1.4;
}
</style>

<script>
let progressEventSource = null;
let allElements = []; // 存储所有元素
let currentBatch = 0; // 当前批次

// 添加进度日志
function addProgressLog(message, logType = 'general-progress') {
    const logsContainer = document.getElementById('progressLogs');
    const now = new Date();
    const timeStr = now.toLocaleTimeString();
    
    const logItem = document.createElement('div');
    logItem.className = `progress-log-item ${logType}`;
    logItem.innerHTML = `
        <div class="progress-log-time">${timeStr}</div>
        <div class="progress-log-message">${message}</div>
    `;
    
    logsContainer.appendChild(logItem);
    logsContainer.scrollTop = logsContainer.scrollHeight;
}

// 清除进度日志
function clearLogs() {
    document.getElementById('progressLogs').innerHTML = '';
}

// 连接到SSE进度更新
function connectToProgress() {
    if (progressEventSource) {
        progressEventSource.close();
    }
    
    progressEventSource = new EventSource('/api/progress');
    progressEventSource.onmessage = function(event) {
        const data = JSON.parse(event.data);
        
        // 忽略心跳消息
        if (data.type === 'heartbeat') {
            return;
        }
        
        // 更新进度条
        updateProgress(data);

        // 添加进度日志（所有类型的消息都记录）
        if (data.message) {
            addProgressLog(`【进度: ${data.message}】`);
        }

        // 如果消息中包含元素信息，更新元素列表
        if (data.element) {
            addElement(data.element);
            // 重新应用过滤器以更新显示
            applyFilters();
        }
    };

    progressEventSource.onerror = function(error) {
        console.error('SSE连接错误:', error);
        // 尝试重新连接
        setTimeout(connectToProgress, 1000);
    };
}

// 更新进度显示
function updateProgress(data) {
    const progressBar = document.querySelector('#progressModal .progress-bar');
    const progressMessage = document.querySelector('#progressMessage');
    
    if (data.progress !== null) {
        progressBar.style.width = `${data.progress}%`;
    }
    
    if (data.message) {
        progressMessage.textContent = data.message;
    }
    
    // 如果进度达到100%，3秒后关闭弹窗
    if (data.progress === 100) {
        setTimeout(() => {
            closeProgress();
        }, 3000);
    }
}

// 关闭进度显示
function closeProgress() {
    if (progressEventSource) {
        progressEventSource.close();
        progressEventSource = null;
    }
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('progressModal'));
    if (modal) {
        modal.hide();
    }
}

// 显示进度弹窗
function showProgress() {
    const modal = new bootstrap.Modal(document.getElementById('progressModal'));
    modal.show();
    
    // 重置进度条和消息
    const progressBar = document.querySelector('#progressModal .progress-bar');
    const progressMessage = document.querySelector('#progressMessage');
    progressBar.style.width = '0%';
    progressMessage.textContent = '准备中...';
    
    // 连接到进度更新
    connectToProgress();
}

// 获取当前设置
function getCurrentSettings() {
    return {
        packageName: document.getElementById('packageSelect').value,
        mainActivity: document.getElementById('mainActivity').value.trim()
    };
}

// 添加单个元素
function addElement(element) {
    // 检查是否已存在相同ID的元素
    const existingIndex = allElements.findIndex(e => e.id === element.id);
    if (existingIndex === -1) {
        allElements.push(element);
    } else {
        allElements[existingIndex] = element;
    }
    applyFilters();
}

// 应用所有过滤器
function applyFilters() {
    const resourceFilter = document.getElementById('resourceFilter').value;
    const textFilter = document.getElementById('textFilter').value;
    let filteredElements = [...allElements];

    // 应用资源ID过滤
    if (resourceFilter === 'hasResource') {
        filteredElements = filteredElements.filter(element => element.resource_id);
    } else if (resourceFilter === 'noResource') {
        filteredElements = filteredElements.filter(element => !element.resource_id);
    }

    // 应用文本过滤
    if (textFilter === 'hasText') {
        filteredElements = filteredElements.filter(element => element.text);
    } else if (textFilter === 'noText') {
        filteredElements = filteredElements.filter(element => !element.text);
    }

    // 显示元素并更新计数
    displayElements(filteredElements);
    updateElementCount(filteredElements.length);
}

// 更新元素计数
function updateElementCount(count) {
    const countElement = document.getElementById('elementCount');
    countElement.textContent = `${count} 个元素`;
}

// 获取元素列表
async function getElements() {
    try {
        const settings = getCurrentSettings();
        if (!settings.packageName) {
            alert('请选择应用包名');
            return;
        }
        
        // 重置元素列表和进度日志
        allElements = [];
        document.getElementById('elementsTable').innerHTML = '';
        updateElementCount(0);
        clearLogs();
        
        // 设置默认过滤条件为"有资源ID"
        document.getElementById('resourceFilter').value = 'hasResource';
        
        showProgress();
        
        // 先关闭之前的连接（如果有）
        if (progressEventSource) {
            progressEventSource.close();
        }
        
        // 建立新的SSE连接
        connectToProgress();
        
        const response = await fetch(`/api/devices/elements?package_name=${settings.packageName}&main_activity=${settings.mainActivity}`);
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || '获取元素失败');
        }
    } catch (error) {
        alert('获取元素失败：' + error);
        if (progressEventSource) {
            progressEventSource.close();
        }
    }
}

// 显示元素列表
function displayElements(elements) {
    const tbody = document.getElementById('elementsTable');
    tbody.innerHTML = elements.map((element, index) => `
        <tr>
            <td>${index + 1}</td>
            <td>${element.type || '-'}</td>
            <td>${element.text || '-'}</td>
            <td>${element.resource_id || '-'}</td>
            <td>${element.package || '-'}</td>
            <td>${element.xpath || '-'}</td>
            <td>${element.clickable ? '是' : '否'}</td>
        </tr>
    `).join('');
}

// 加载包名列表
async function loadPackages() {
    try {
        const response = await fetch('/api/devices/packages');
        const packages = await response.json();
        
        if (response.ok) {
            const select = document.getElementById('packageSelect');
            packages.forEach(pkg => {
                const option = document.createElement('option');
                option.value = pkg;
                option.textContent = pkg;
                select.appendChild(option);
            });

            // 设置默认选中的包名
            const defaultPackage = 'com.olight.omall';
            select.value = defaultPackage;
            
            // 如果选中的是默认包名，自动填充主Activity
            if (select.value === defaultPackage) {
                document.getElementById('mainActivity').value = 'com.olightmall.core.login.ui.activity.SplashActivity';
            }

            // 添加包名选择事件监听
            select.addEventListener('change', function(e) {
                const mainActivityInput = document.getElementById('mainActivity');
                if (e.target.value === 'com.olight.omall') {
                    mainActivityInput.value = 'com.olightmall.core.login.ui.activity.SplashActivity';
                } else {
                    mainActivityInput.value = '';
                }
            });

            // 自动触发获取元素
            getElements();
        } else {
            alert('加载包名列表失败：' + packages.error);
        }
    } catch (error) {
        alert('加载包名列表失败：' + error);
    }
}

// 页面加载完成后加载包名列表
document.addEventListener('DOMContentLoaded', function() {
    loadPackages();
    // 设置默认过滤条件为"有资源ID"
    document.getElementById('resourceFilter').value = 'hasResource';
});
</script>
{% endblock %} 