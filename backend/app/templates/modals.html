<!-- 新建测试用例模态框 -->
<div class="modal fade" id="createTestCaseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新建测试用例</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createTestCaseForm">
                    <div class="mb-3">
                        <label class="form-label">名称</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">项目ID</label>
                        <input type="text" class="form-control" name="project_id" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createTestCase()">创建</button>
            </div>
        </div>
    </div>
</div>

<!-- 登录模态框 -->
<div class="modal fade" id="loginModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">登录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="loginForm">
                    <div class="mb-3">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-control" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">密码</label>
                        <input type="password" class="form-control" name="password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="login()">登录</button>
            </div>
        </div>
    </div>
</div>

<script>
// 显示创建测试用例模态框
function showCreateTestCaseModal() {
    const modal = new bootstrap.Modal(document.getElementById('createTestCaseModal'));
    modal.show();
}

// 创建测试用例
async function createTestCase() {
    const form = document.getElementById('createTestCaseForm');
    const formData = new FormData(form);
    const data = {
        name: formData.get('name'),
        description: formData.get('description'),
        project_id: formData.get('project_id'),
        steps: [],
        status: 'active'
    };

    try {
        const response = await fetch('/api/test-cases/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            window.location.reload();
        } else {
            const errorData = await response.json();
            alert(errorData.detail || '创建失败');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('创建失败: ' + error.message);
    }
}

// 显示登录模态框
function showLoginModal() {
    const modal = new bootstrap.Modal(document.getElementById('loginModal'));
    modal.show();
}

// 登录
async function login() {
    const form = document.getElementById('loginForm');
    const formData = new FormData(form);
    const data = {
        username: formData.get('username'),
        password: formData.get('password')
    };

    try {
        const response = await fetch('/api/auth/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            const result = await response.json();
            localStorage.setItem('token', result.access_token);
            window.location.reload();
        } else {
            const errorData = await response.json();
            alert(errorData.detail || '登录失败');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('登录失败: ' + error.message);
    }
}
</script> 