{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- 设置导航 -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <ul class="nav nav-pills" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#users">用户管理</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#roles">角色权限</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="tab-content">
        <!-- 用户管理 -->
        <div class="tab-pane fade show active" id="users">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">用户列表</h5>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="bi bi-person-plus"></i> 添加用户
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>姓名</th>
                                    <th>角色</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>{{ user.username }}</td>
                                    <td>{{ user.email }}</td>
                                    <td>{{ user.full_name or '' }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'primary' if user.role == 'admin' else 'secondary' }}">
                                            {{ user.role }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if not user.disabled else 'danger' }}">
                                            {{ '启用' if not user.disabled else '禁用' }}
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editUser('{{ user.username }}')">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-sm btn-{{ 'danger' if not user.disabled else 'success' }}" 
                                                onclick="toggleUserStatus('{{ user.username }}')">
                                            <i class="bi bi-{{ 'lock' if not user.disabled else 'unlock' }}"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteUser('{{ user.username }}')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 角色权限 -->
        <div class="tab-pane fade" id="roles">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">角色权限设置</h5>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRoleModal">
                        <i class="bi bi-plus-lg"></i> 添加角色
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>角色名称</th>
                                    <th>描述</th>
                                    <th>权限</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for role in roles %}
                                <tr>
                                    <td>{{ role.name }}</td>
                                    <td>{{ role.description }}</td>
                                    <td>
                                        {% for permission in role.permissions %}
                                        <span class="badge bg-info me-1">{{ permission }}</span>
                                        {% endfor %}
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editRole('{{ role.name }}')">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteRole('{{ role.name }}')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加用户模态框 -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="fullName" class="form-label">姓名</label>
                        <input type="text" class="form-control" id="fullName">
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">角色</label>
                        <select class="form-select" id="role" required>
                            <option value="user">普通用户</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createUser()">创建</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加角色模态框 -->
<div class="modal fade" id="addRoleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加角色</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addRoleForm">
                    <div class="mb-3">
                        <label for="roleName" class="form-label">角色名称</label>
                        <input type="text" class="form-control" id="roleName" required>
                    </div>
                    <div class="mb-3">
                        <label for="roleDescription" class="form-label">角色描述</label>
                        <textarea class="form-control" id="roleDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">权限设置</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="permProject" value="project_manage">
                            <label class="form-check-label" for="permProject">项目管理</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="permTestCase" value="testcase_manage">
                            <label class="form-check-label" for="permTestCase">测试用例管理</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="permResult" value="result_manage">
                            <label class="form-check-label" for="permResult">测试结果管理</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="permUser" value="user_manage">
                            <label class="form-check-label" for="permUser">用户管理</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="permRole" value="role_manage">
                            <label class="form-check-label" for="permRole">角色管理</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createRole()">创建</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    async function createUser() {
        const username = document.getElementById('username').value;
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const fullName = document.getElementById('fullName').value;
        const role = document.getElementById('role').value;
        
        try {
            const response = await fetch('/api/auth/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username,
                    email,
                    password,
                    full_name: fullName,
                    role
                }),
            });
            
            if (response.ok) {
                window.location.reload();
            } else {
                const error = await response.json();
                alert('创建用户失败：' + error.detail);
            }
        } catch (error) {
            alert('创建用户失败：' + error.message);
        }
    }

    async function toggleUserStatus(username) {
        try {
            const response = await fetch(`/api/users/${username}/toggle-status`, {
                method: 'POST',
            });
            
            if (response.ok) {
                window.location.reload();
            } else {
                const error = await response.json();
                alert('切换用户状态失败：' + error.detail);
            }
        } catch (error) {
            alert('切换用户状态失败：' + error.message);
        }
    }

    async function deleteUser(username) {
        if (!confirm('确定要删除这个用户吗？此操作不可恢复！')) {
            return;
        }
        
        try {
            const response = await fetch(`/api/users/${username}`, {
                method: 'DELETE',
            });
            
            if (response.ok) {
                window.location.reload();
            } else {
                const error = await response.json();
                alert('删除用户失败：' + error.detail);
            }
        } catch (error) {
            alert('删除用户失败：' + error.message);
        }
    }

    async function createRole() {
        const name = document.getElementById('roleName').value;
        const description = document.getElementById('roleDescription').value;
        const permissions = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
            .map(checkbox => checkbox.value);
        
        try {
            const response = await fetch('/api/roles/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name,
                    description,
                    permissions
                }),
            });
            
            if (response.ok) {
                window.location.reload();
            } else {
                const error = await response.json();
                alert('创建角色失败：' + error.detail);
            }
        } catch (error) {
            alert('创建角色失败：' + error.message);
        }
    }

    async function deleteRole(roleName) {
        if (!confirm('确定要删除这个角色吗？此操作不可恢复！')) {
            return;
        }
        
        try {
            const response = await fetch(`/api/roles/${roleName}`, {
                method: 'DELETE',
            });
            
            if (response.ok) {
                window.location.reload();
            } else {
                const error = await response.json();
                alert('删除角色失败：' + error.detail);
            }
        } catch (error) {
            alert('删除角色失败：' + error.message);
        }
    }

    function editUser(username) {
        // TODO: 实现编辑用户功能
        alert('编辑用户功能待实现');
    }

    function editRole(roleName) {
        // TODO: 实现编辑角色功能
        alert('编辑角色功能待实现');
    }
</script>
{% endblock %} 