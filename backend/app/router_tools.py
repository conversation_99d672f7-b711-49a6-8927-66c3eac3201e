from flask import Blueprint, render_template, jsonify, request, Response
from core.device_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>
from get_page_element import <PERSON><PERSON><PERSON>Fetcher
import queue
import json

class RouterTools:
    def __init__(self, app):
        self.app = app
        self.device_manager = DeviceManager()
        self.page_element_fetcher = PageElementFetcher(self.device_manager)
        self.progress_queue = queue.Queue()
        self.register_routes()

    def progress_callback(self, data):
        self.progress_queue.put(data)

    def register_routes(self):
        self.device_manager.set_progress_callback(self.progress_callback)

        @self.app.route('/')
        def index():
            return render_template('index.html')

        @self.app.route('/device-elements')
        def device_elements():
            return render_template('device_elements.html')

        @self.app.route('/api/devices/elements')
        def get_elements():
            try:
                package_name = request.args.get('package_name')
                main_activity = request.args.get('main_activity')
                elements = self.page_element_fetcher.get_elements(package_name, main_activity)
                return jsonify(elements)
            except Exception as e:
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/devices/capture-element', methods=['POST'])
        def capture_element():
            try:
                data = request.get_json()
                package_name = data.get('package_name')
                main_activity = data.get('main_activity')
                element = self.page_element_fetcher.capture_element(package_name, main_activity)
                return jsonify(element)
            except Exception as e:
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/devices/packages')
        def get_packages():
            try:
                packages = self.device_manager.get_installed_packages()
                return jsonify(packages)
            except Exception as e:
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/progress')
        def progress():
            def generate_progress():
                while True:
                    try:
                        data = {'type': 'heartbeat'}
                        yield f"data: {json.dumps(data)}\n\n"
                        try:
                            data = self.progress_queue.get(timeout=5)
                            yield f"data: {json.dumps(data)}\n\n"
                        except queue.Empty:
                            continue
                    except GeneratorExit:
                        break
                    except Exception as e:
                        print(f"生成进度事件时出错: {str(e)}")
                        break
            return Response(
                generate_progress(),
                mimetype='text/event-stream',
                headers={
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive'
                }
            ) 