from flask import Blueprint, render_template, request, jsonify, redirect, url_for, Response
from models.project import Project, TestCase
from database import db
import threading
import queue
import json
import time
from uiautomator2 import Device


project_bp = Blueprint('project', __name__)

# 执行队列和结果字典
execution_queues = {}
execution_results = {}

@project_bp.route('/projects')
def list_projects():
    projects = Project.query.all()
    return render_template('projects.html', projects=projects, active_page='projects')

@project_bp.route('/projects/new', methods=['GET', 'POST'])
def new_project():
    if request.method == 'POST':
        data = request.get_json()
        name = data.get('name')
        description = data.get('description')
        
        # 打印请求数据
        print('Request JSON data:', data)
        print('Request JSON name:', name)
        
        # 数据验证
        if not name:
            return jsonify({'status': 'error', 'message': 'Project name is required'}), 400
        
        project = Project(name=name, description=description)
        db.session.add(project)
        db.session.commit()
        
        # 打印响应数据
        response_data = {'status': 'success', 'message': 'Project created successfully'}
        print('Response data:', response_data)
        
        return jsonify(response_data)
    return render_template('new_project.html')

@project_bp.route('/projects/<int:project_id>/testcases', methods=['GET', 'POST'])
def list_testcases(project_id):
    """创建测试用例"""
    try:
        project = Project.query.filter_by(id=project_id).first()
        if not project:
            return jsonify({'error': '项目不存在'}), 404

              
        data = request.get_json()
        if not data or not data.get('name'):
            return jsonify({'error': '用例名称不能为空'}), 400
            
        # 验证测试步骤
        steps = data.get('steps', [])
        if not isinstance(steps, list):
            return jsonify({'error': '测试步骤格式错误'}), 400


        # 验证每个步骤的格式
        for step in steps:
            if not isinstance(step, dict) or 'type' not in step or 'params' not in step:
                return jsonify({'error': '测试步骤格式错误'}), 400
                
            # 验证步骤类型
            if step['type'] not in ['click', 'input', 'scroll', 'verify', 'wait', 'set_text']:
                return jsonify({'error': f'不支持的步骤类型：{step["type"]}'}), 400
                
            # 验证参数
            params = step['params']
            if not isinstance(params, dict):
                return jsonify({'error': '步骤参数格式错误'}), 400
                
            # 根据步骤类型验证必需参数
            if step['type'] in ['click', 'input', 'verify']:
                if 'selectorType' not in params or 'selectorValue' not in params:
                    return jsonify({'error': '缺少必需的选择器参数'}), 400
                if step['type'] == 'input' and 'inputValue' not in params:
                    return jsonify({'error': '缺少输入值参数'}), 400
                if step['type'] == 'verify':
                    if 'verifyType' not in params or 'expectedValue' not in params:
                        return jsonify({'error': '缺少验证参数'}), 400
            elif step['type'] == 'scroll':
                if 'direction' not in params or 'distance' not in params:
                    return jsonify({'error': '缺少滚动参数'}), 400
            elif step['type'] == 'wait':
                if 'seconds' not in params:
                    return jsonify({'error': '缺少等待时间参数'}), 400
            
        test_case = TestCase(
            name=data['name'],
            description=data.get('description', ''),
            project_id=project_id,
            steps=steps  # 保存完整的步骤数据
        )

        db.session.add(test_case)
        db.session.commit()
        
        return jsonify({
            'id': test_case.id,
            'name': test_case.name,
            'description': test_case.description,
            'project_id': test_case.project_id,
            'steps': test_case.steps
        })
    except Exception as e:
        db.session.rollback()
        print(f"创建测试用例时出错: {str(e)}")
        return jsonify({'error': '创建测试用例失败，请稍后重试'}), 500

@project_bp.route('/projects/<int:project_id>/testcases/new', methods=['GET', 'POST'])
def new_testcase(project_id):
    project = Project.query.get_or_404(project_id)
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        testcase = TestCase(name=name, description=description, project_id=project_id)
        db.session.add(testcase)
        db.session.commit()
        return redirect(url_for('project.list_testcases', project_id=project_id))
    return render_template('new_testcase.html', project=project)

@project_bp.route('/testcases/<int:testcase_id>/edit', methods=['GET', 'POST'])
def edit_testcase(testcase_id):
    testcase = TestCase.query.get_or_404(testcase_id)
    if request.method == 'POST':
        testcase.name = request.form.get('name')
        testcase.description = request.form.get('description')
        db.session.commit()
        return redirect(url_for('project.list_testcases', project_id=testcase.project_id))
    return render_template('edit_testcase.html', testcase=testcase)

@project_bp.route('/testcases/<int:testcase_id>/execute', methods=['POST'])
def execute_test_case(testcase_id):
    """开始执行测试用例"""
    try:
        print(f"收到执行测试用例 {testcase_id} 的请求")  # 调试日志
        test_case = TestCase.query.filter_by(id=testcase_id).first()
        if not test_case:
            print(f"错误：测试用例 {testcase_id} 不存在")  # 调试日志
            return jsonify({'error': '测试用例不存在'}), 404
            
        print(f"测试用例 {testcase_id} 信息：{test_case.name}")  # 调试日志
        
        # 创建执行队列
        if testcase_id not in execution_queues:
            print(f"为测试用例 {testcase_id} 创建新的执行队列")  # 调试日志
            execution_queues[testcase_id] = queue.Queue()
        else:
            print(f"测试用例 {testcase_id} 的执行队列已存在")  # 调试日志
            
        # 启动执行线程
        thread = threading.Thread(
            target=execute_test_steps,
            args=(testcase_id, test_case.steps)
        )
        thread.daemon = True
        print(f"启动测试用例 {testcase_id} 的执行线程")  # 调试日志
        thread.start()
        
        return jsonify({'message': '测试用例开始执行'})
    except Exception as e:
        print(f"启动测试用例 {testcase_id} 执行时出错: {str(e)}")  # 调试日志
        import traceback
        print(f"错误堆栈: {traceback.format_exc()}")  # 调试日志
        return jsonify({'error': '启动执行失败，请稍后重试'}), 500

@project_bp.route('/testcases/<int:testcase_id>/execute', methods=['GET'])
def get_execution_status(testcase_id):
    """获取测试用例执行状态"""
    def generate_status():
        if testcase_id not in execution_queues:
            yield f"data: {json.dumps({'type': 'error', 'message': '测试用例未在执行'})}\n\n"
            return
            
        queue = execution_queues[testcase_id]
        
        while True:
            try:
                data = queue.get(timeout=30)  # 30秒超时
                
                # 确保数据是字典类型
                if isinstance(data, dict):
                    yield f"data: {json.dumps(data)}\n\n"
                else:
                    print(f"警告：收到非字典类型数据：{data}")
                    yield f"data: {json.dumps({'type': 'warning', 'message': '收到无效数据格式'})}\n\n"
                
                if data.get('type') == 'complete':
                    break
            except queue.Empty:
                print(f"测试用例 {testcase_id} 状态更新超时，发送心跳")  # 调试日志
                yield f"data: {json.dumps({'type': 'heartbeat'})}\n\n"
            except Exception as e:
                print(f"处理状态更新时出错：{str(e)}")  # 调试日志
                yield f"data: {json.dumps({'type': 'error', 'message': f'处理状态更新时出错：{str(e)}'})}\n\n"
                break
                
    return Response(
        generate_status(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # 禁用 Nginx 缓冲
        }
    )

@project_bp.route('/testcases/<int:testcase_id>/delete', methods=['POST'])
def delete_testcase(testcase_id):
    testcase = TestCase.query.get_or_404(testcase_id)
    db.session.delete(testcase)
    db.session.commit()
    return redirect(url_for('project.list_testcases', project_id=testcase.project_id)) 

@project_bp.route('/testcases/<int:testcase_id>', methods=['GET', 'PUT'])
def update_test_case(testcase_id):
    """更新测试用例"""
    try:
        test_case = TestCase.query.filter_by(id=testcase_id).first()
        if not test_case:
            return jsonify({'error': '测试用例不存在'}), 404
        
        if request.method == 'GET':
            return jsonify({
                'id': test_case.id,
                'name': test_case.name,
                'description': test_case.description,
                'project_id': test_case.project_id,
                'steps': test_case.steps
            })
             
        if request.method == 'PUT':
            data = request.get_json()
            test_case.name = data.get('name')
            test_case.description = data.get('description')
            test_case.steps = data.get('steps')
            db.session.commit()

        print(f"update_test_case test_case: {test_case}")

        return jsonify({
            'id': test_case.id,
            'name': test_case.name,
            'description': test_case.description,
            'project_id': test_case.project_id,
            'steps': test_case.steps
        })
    except Exception as e:
        print(f"更新测试用例时出错: {str(e)}")
        return jsonify({'error': '更新测试用例失败，请稍后重试'}), 500
    


class DeviceManager:
    def __init__(self):
        self._device = None
        self._progress_callback = None
        
    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self._progress_callback = callback
        
    def ensure_device(self):
        """确保设备已连接"""
        try:
            if not self._device:
                print("正在初始化设备连接...")  # 调试日志
                # 使用更基础的设备连接方式
                import uiautomator2 as u2
                # 获取设备序列号
                import subprocess
                result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
                print(f"ADB设备列表: {result.stdout}")  # 调试日志
                
                if "device" not in result.stdout:
                    raise Exception("未找到已授权的设备")
                    
                # 获取第一个设备的序列号
                lines = result.stdout.strip().split('\n')
                if len(lines) < 2:
                    raise Exception("未找到设备")
                    
                device_serial = lines[1].split('\t')[0]
                print(f"设备序列号: {device_serial}")  # 调试日志
                
                # 使用序列号连接设备
                self._device = u2.connect(device_serial)
                print("设备对象已创建")  # 调试日志
                
            # 检查设备是否真正可用
            print("正在检查设备状态...")  # 调试日志
            device_info = self._device.info
            print(f"设备信息: {device_info}")  # 调试日志
            
            print("ensure_device 设备连接成功")  # 调试日志
            return True
            
        except Exception as e:
            error_msg = str(e)
            print(f"设备连接错误: {error_msg}")  # 调试日志
            
            if "expected string or bytes-like object" in error_msg:
                raise Exception("设备连接失败，请检查：\n1. 设备是否已通过USB连接\n2. 是否已启用开发者选项和USB调试\n3. 是否已在设备上授权USB调试")
            elif "device not found" in error_msg.lower():
                raise Exception("未找到设备，请检查：\n1. 设备是否已通过USB连接\n2. USB线是否正常工作\n3. 设备是否已开机")
            elif "connection refused" in error_msg.lower():
                raise Exception("连接被拒绝，请检查：\n1. 设备是否已启用USB调试\n2. 是否已在设备上授权USB调试\n3. 尝试重新插拔USB线")
            else:
                raise Exception(f"设备连接失败: {error_msg}\n请确保：\n1. 设备已通过USB连接\n2. 已启用开发者选项和USB调试\n3. 已授权USB调试权限")
            
    def _report_progress(self, data):
        """报告进度"""
        if self._progress_callback:
            self._progress_callback(data)
            
    def find_element(self, selector_type, selector_value, element_index=0):
        """查找元素"""
        self.ensure_device()

        try:
            if selector_type == 'id':
                element = self._device(resourceId=selector_value)
            elif selector_type == 'id_index':
                # 使用XPath查找指定索引的元素 (XPath索引从1开始)
                xpath = f'//*[@resource-id="{selector_value}"][{element_index + 1}]'
                element = self._device.xpath(xpath)

                if not element.exists:
                    # 检查是否有任何匹配的元素
                    any_xpath = f'//*[@resource-id="{selector_value}"]'
                    if not self._device.xpath(any_xpath).exists:
                        raise Exception(f'未找到任何匹配的元素: [resourceId="{selector_value}"]')

                    # 计算实际元素数量
                    count = 0
                    for i in range(1, 21):  # 最多检查20个元素
                        test_xpath = f'//*[@resource-id="{selector_value}"][{i}]'
                        if self._device.xpath(test_xpath).exists:
                            count = i
                        else:
                            break
                    raise Exception(f'索引超出范围: 找到 {count} 个元素，但请求索引 {element_index} (第{element_index + 1}个)')

                print(f"[DEBUG] 成功找到元素: resourceId={selector_value}, index={element_index}")
            elif selector_type == 'text':
                element = self._device(text=selector_value)
            elif selector_type == 'xpath':
                element = self._device.xpath(selector_value)
            elif selector_type == 'class_name':
                element = self._device(className=selector_value)
            else:
                raise ValueError(f'不支持的选择器类型: {selector_type}')

            # 验证元素是否存在
            if not element.exists:
                raise Exception(f'未找到元素: [{selector_type}="{selector_value}"]')

            return element
        except Exception as e:
            raise Exception(f'查找元素失败: {str(e)}')
            
    def scroll_up(self, distance):
        """向上滚动"""
        self.ensure_device()
        self._device.swipe_ext("up", scale=distance/1000)
        
    def scroll_down(self, distance):
        """向下滚动"""
        self.ensure_device()
        self._device.swipe_ext("down", scale=distance/1000)
        
    def get_installed_packages(self):
        """获取已安装的应用包名列表"""
        self.ensure_device()
        return self._device.app_list()
        
    def get_elements(self, package_name, main_activity=None):
        """获取应用的元素列表"""
        self.ensure_device()
        
        try:
            # 启动应用
            if main_activity:
                self._device.app_start(package_name, main_activity)
            else:
                self._device.app_start(package_name)
                
            time.sleep(2)  # 等待应用启动
            
            # 获取当前界面的层级结构
            xml_data = self._device.dump_hierarchy()
            
            # 解析XML并提取元素信息
            # TODO: 实现XML解析和元素提取逻辑
            
            return []
            
        except Exception as e:
            raise Exception(f'获取元素失败: {str(e)}')
            
    def capture_element(self, package_name, main_activity=None):
        """捕获元素"""
        self.ensure_device()

        try:
            # 启动应用
            if main_activity:
                self._device.app_start(package_name, main_activity)
            else:
                self._device.app_start(package_name)

            time.sleep(2)  # 等待应用启动

            # 等待用户点击元素
            # TODO: 实现元素捕获逻辑

            return {}

        except Exception as e:
            raise Exception(f'捕获元素失败: {str(e)}')

    def count_elements_by_id(self, resource_id):
        """统计指定resource-id的元素数量"""
        self.ensure_device()

        try:
            count = 0
            for i in range(1, 21):  # 最多检查20个元素
                xpath = f'//*[@resource-id="{resource_id}"][{i}]'
                if self._device.xpath(xpath).exists:
                    count = i
                else:
                    break
            return count
        except Exception as e:
            print(f"统计元素数量失败: {str(e)}")
            return 0


# 创建全局设备管理器实例
device_manager = DeviceManager()
# 进度队列
progress_queue = queue.Queue()


def progress_callback(data):
    """进度回调函数"""
    progress_queue.put(data)

# 设置进度回调
device_manager.set_progress_callback(progress_callback)

def execute_test_steps(testcase_id, steps):
    """执行测试步骤"""
    print(f"开始执行测试用例 {testcase_id} 的步骤")  # 调试日志
    queue = execution_queues[testcase_id]
    success = True
    total_steps = len(steps)

    # 智能索引分配：统计相同ID的使用次数
    id_usage_count = {}

    try:
        # 首先检查设备连接
        try:
            print("检查设备连接状态...")  # 调试日志
            device_manager.ensure_device()  # 使用正确的方法名
            print("设备连接成功")  # 调试日志
            queue.put({
                'type': 'progress',
                'progress': 0,
                'status': '设备已连接',
                'message': '设备连接成功，开始执行测试步骤',
                'level': 'success'
            })
        except Exception as e:
            print(f"设备连接失败: {str(e)}")  # 调试日志
            queue.put({
                'type': 'progress',
                'progress': 0,
                'status': '设备连接失败',
                'message': str(e),
                'level': 'error',
                'error': '设备连接失败',
                'stack': str(e)
            })
            raise
        
        for index, step in enumerate(steps, 1):
            # 智能索引分配：为相同ID的元素自动分配索引
            if step.get('type') in ['click', 'input', 'verify', 'set_text']:
                params = step.get('params', {})
                selector_type = params.get('selectorType')
                selector_value = params.get('selectorValue')

                # 优先使用用户指定的索引，如果没有指定且是ID选择器，则自动分配
                if 'elementIndex' in params:
                    print(f"[用户指定] ID '{selector_value}' 使用用户指定索引: {params['elementIndex']}")
                elif selector_type in ['id', 'ID']:
                    if selector_value not in id_usage_count:
                        id_usage_count[selector_value] = 0
                    params['elementIndex'] = id_usage_count[selector_value]
                    id_usage_count[selector_value] += 1
                    print(f"[智能索引] 为ID '{selector_value}' 自动分配索引: {params['elementIndex']}")

            # 更新进度
            progress = (index - 1) / total_steps * 100
            status_data = {
                'type': 'progress',
                'progress': progress,
                'status': f'执行步骤 {index}/{total_steps}',
                'message': f'开始执行: {get_step_description(step)}',
                'level': 'info'
            }
            print(f"发送进度更新：{status_data}")  # 调试日志
            queue.put(status_data)

            # 执行步骤
            try:
                execute_single_step(step, device_manager)
                success_data = {
                    'type': 'progress',
                    'progress': index / total_steps * 100,
                    'message': f'步骤 {index} 执行成功',
                    'level': 'success'
                }
                print(f"发送成功状态：{success_data}")  # 调试日志
                queue.put(success_data)
            except Exception as e:
                success = False
                error_message = str(e)
                stack_trace = None
                
                # 获取详细的错误堆栈
                import traceback
                stack_trace = traceback.format_exc()
                
                error_data = {
                    'type': 'progress',
                    'progress': index / total_steps * 100,
                    'message': f'步骤 {index} 执行失败: {error_message}',
                    'level': 'error',
                    'error': error_message,
                    'stack': stack_trace
                }
                print(f"发送错误状态：{error_data}")  # 调试日志
                queue.put(error_data)
                break
                
            # 每个步骤之间稍作延迟
            time.sleep(0.5)
            
    except Exception as e:
        success = False
        error_message = str(e)
        stack_trace = None
        
        # 获取详细的错误堆栈
        import traceback
        stack_trace = traceback.format_exc()
        
        error_data = {
            'type': 'progress',
            'progress': 100,
            'message': f'执行过程出错: {error_message}',
            'level': 'error',
            'error': error_message,
            'stack': stack_trace
        }
        print(f"发送最终错误状态：{error_data}")  # 调试日志
        queue.put(error_data)
    finally:
        # 发送完成状态
        complete_data = {
            'type': 'complete',
            'success': success,
            'message': '执行完成' if success else '执行失败'
        }
        print(f"发送完成状态：{complete_data}")  # 调试日志
        queue.put(complete_data)
        
        # 清理资源
        if testcase_id in execution_queues:
            del execution_queues[testcase_id]



def execute_single_step(step, device_manager):
    """执行单个测试步骤"""
    step_type = step['type']
    params = step['params']
    
    try:
        if step_type in ['click', 'input', 'verify']:
            # 获取元素索引（支持智能分配的索引）
            element_index = params.get('elementIndex', 0)
            print(f"[DEBUG] {step_type} step: {step}")
            print(f"[DEBUG] {step_type} params: {params}")
            print(f"[DEBUG] 最终使用的元素索引: {element_index}")
            element = device_manager.find_element(
                selector_type=params['selectorType'],
                selector_value=params['selectorValue'],
                element_index=element_index
            )
            
            if step_type == 'click':
                element.click()
            elif step_type == 'input':
                element.send_keys(params['inputValue'])
            elif step_type == 'verify':
                verify_type = params['verifyType']
                expected_value = params['expectedValue']
                
                if verify_type == 'text':
                    actual_value = element.text
                    if actual_value != expected_value:
                        raise Exception(f'文本验证失败: 期望 "{expected_value}", 实际 "{actual_value}"')
                elif verify_type == 'visible':
                    if element.is_displayed() != (expected_value.lower() == 'true'):
                        raise Exception('可见性验证失败')
                elif verify_type == 'enabled':
                    if element.is_enabled() != (expected_value.lower() == 'true'):
                        raise Exception('可用性验证失败')
        elif step_type == 'set_text':
            print(f"[DEBUG] set_text step: {step}")
            print(f"[DEBUG] set_text params: {params}")
            # 获取元素并设置文本内容（支持智能分配的索引）
            element_index = params.get('elementIndex', 0)
            print(f"[DEBUG] 最终使用的元素索引: {element_index}")
            element = device_manager.find_element(
                selector_type=params['selectorType'],
                selector_value=params['selectorValue'],
                element_index=element_index
            )
            if hasattr(element, 'set_text'):
                element.set_text(params['inputValue'])
            else:
                element.send_keys(params['inputValue'])
        elif step_type == 'scroll':
            direction = params['direction']
            distance = int(params['distance'])
            
            if direction == 'up':
                device_manager.scroll_up(distance)
            else:
                device_manager.scroll_down(distance)
        elif step_type == 'wait':
            seconds = int(params['seconds'])
            time.sleep(seconds)
    except Exception as e:
        # 重新抛出异常，添加更多上下文信息
        raise Exception(f'执行步骤 "{get_step_description(step)}" 失败: {str(e)}')      
    

def get_step_description(step):
    """获取步骤的描述文本"""
    step_type = step['type']
    params = step['params']
    if step_type == 'click':
        return f'点击元素 [{params.get("selectorType", "")}="{params.get("selectorValue", "")}"]'
    elif step_type == 'input':
        return f'输入文本 "{params.get("inputValue", "")}" 到元素 [{params.get("selectorType", "")}="{params.get("selectorValue", "")}"]'
    elif step_type == 'set_text':
        return f'设置元素 [{params.get("selectorType", "")}="{params.get("selectorValue", "")}"] 的文本为 "{params.get("inputValue", "")}"'
    elif step_type == 'scroll':
        return f'向{params.get("direction", "up") == "up" and "上" or "下"}滚动 {params.get("distance", "")} 像素'
    elif step_type == 'verify':
        return f'验证元素 [{params.get("selectorType", "")}="{params.get("selectorValue", "")}"] 的{params.get("verifyType", "")}'
    elif step_type == 'wait':
        return f'等待 {params.get("seconds", "")} 秒'
    return '未知步骤'

def generate_progress():
    """生成进度事件流"""
    while True:
        try:
            # 每5秒发送一次心跳
            data = {'type': 'heartbeat'}
            yield f"data: {json.dumps(data)}\n\n"
            
            # 检查队列中的进度更新
            try:
                data = progress_queue.get(timeout=5)
                yield f"data: {json.dumps(data)}\n\n"
            except queue.Empty:
                continue
            
        except GeneratorExit:
            break
        except Exception as e:
            print(f"生成进度事件时出错: {str(e)}")
            break

@project_bp.route('/api/progress')
def progress():
    """进度事件流"""
    return Response(
        generate_progress(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive'
        }
    )

@project_bp.route('/api/check-element-count', methods=['POST'])
def check_element_count():
    """检查指定resource-id的元素数量"""
    try:
        data = request.get_json()
        resource_id = data.get('resourceId')

        if not resource_id:
            return jsonify({'error': '缺少resourceId参数'}), 400

        # 统计元素数量
        count = device_manager.count_elements_by_id(resource_id)

        return jsonify({
            'success': True,
            'resourceId': resource_id,
            'count': count,
            'message': f'找到 {count} 个匹配的元素' if count > 0 else '未找到匹配的元素'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@project_bp.teardown_app_request
def shutdown_session(exception=None):
    """关闭数据库会话"""
    db.session.remove()

if __name__ == '__main__':
    project_bp.run(host='0.0.0.0', port=8000, debug=True) 