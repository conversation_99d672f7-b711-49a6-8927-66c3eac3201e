from flask import Blueprint, jsonify, render_template, Response, request
from app.core.device_manager import <PERSON>ceManager
import json
import queue
import threading

device_element_bp = Blueprint('device_element', __name__)
device_manager = DeviceManager()

# 创建一个全局的进度队列
progress_queues = {}

def progress_callback(data):
    """进度回调函数"""
    queue_id = threading.get_ident()
    if queue_id in progress_queues:
        progress_queues[queue_id].put(data)

@device_element_bp.route('/device-elements')
def device_elements_page():
    """渲染设备元素页面"""
    packages = device_manager.get_installed_packages()
    return render_template('device_elements.html', active_page='device_elements', packages=packages)

@device_element_bp.route('/api/devices/elements')
def get_device_elements():
    """获取设备元素列表"""
    try:
        # 创建进度队列
        queue_id = threading.get_ident()
        progress_queues[queue_id] = queue.Queue()
        device_manager.set_progress_callback(progress_callback)
        
        package_name = request.args.get('package_name')
        elements = device_manager.get_elements(package_name)
        
        # 清理队列
        if queue_id in progress_queues:
            del progress_queues[queue_id]
        
        return jsonify(elements)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@device_element_bp.route('/api/devices/capture-element', methods=['POST'])
def capture_element():
    """捕获设备元素"""
    try:
        # 创建进度队列
        queue_id = threading.get_ident()
        progress_queues[queue_id] = queue.Queue()
        device_manager.set_progress_callback(progress_callback)
        
        package_name = request.json.get('package_name')
        element = device_manager.capture_element(package_name)
        
        # 清理队列
        if queue_id in progress_queues:
            del progress_queues[queue_id]
        
        return jsonify(element)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@device_element_bp.route('/api/devices/packages')
def get_packages():
    """获取已安装的应用包名列表"""
    try:
        packages = device_manager.get_installed_packages()
        return jsonify(packages)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@device_element_bp.route('/api/devices/highlight-element/<element_id>', methods=['POST'])
def highlight_element(element_id):
    """高亮显示指定的元素"""
    try:
        device_manager.highlight_element(element_id)
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'detail': str(e)}), 500

@device_element_bp.route('/api/progress')
def get_progress():
    """SSE端点，用于发送进度更新"""
    def generate():
        queue_id = threading.get_ident()
        progress_queues[queue_id] = queue.Queue()
        
        try:
            while True:
                # 等待进度更新
                try:
                    data = progress_queues[queue_id].get(timeout=30)  # 30秒超时
                    yield f"data: {json.dumps(data)}\n\n"
                except queue.Empty:
                    # 发送心跳保持连接
                    yield f"data: {json.dumps({'type': 'heartbeat'})}\n\n"
        finally:
            # 清理队列
            if queue_id in progress_queues:
                del progress_queues[queue_id]
    
    return Response(generate(), mimetype='text/event-stream') 