from flask import Blueprint, jsonify, render_template
from app.core.device_manager import Device<PERSON>anager

device_bp = Blueprint('device', __name__)
device_manager = DeviceManager()

@device_bp.route('/devices')
def devices_page():
    """渲染设备管理页面"""
    return render_template('devices.html', active_page='devices')

@device_bp.route('/api/devices/status')
def get_device_status():
    """获取设备状态"""
    try:
        device_manager.connect_to_device()
        return jsonify({'status': 'connected'})
    except Exception as e:
        return jsonify({'status': 'disconnected', 'error': str(e)}), 500 