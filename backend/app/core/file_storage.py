import os
import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime

class FileStorage:
    def __init__(self, storage_dir: str):
        """初始化文件存储。

        Args:
            storage_dir: 存储目录的路径
        """
        self.storage_dir = storage_dir
        if not os.path.exists(storage_dir):
            os.makedirs(storage_dir)

    def _get_file_path(self, collection: str) -> str:
        """获取集合的文件路径。

        Args:
            collection: 集合名称

        Returns:
            str: 文件路径
        """
        return os.path.join(self.storage_dir, f"{collection}.json")

    def _load_data(self, collection: str) -> List[Dict]:
        """加载集合数据。

        Args:
            collection: 集合名称

        Returns:
            List[Dict]: 集合中的所有数据
        """
        file_path = self._get_file_path(collection)
        if not os.path.exists(file_path):
            return []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            try:
                return json.load(f)
            except json.JSONDecodeError:
                return []

    def _save_data(self, collection: str, data: List[Dict]) -> None:
        """保存集合数据。

        Args:
            collection: 集合名称
            data: 要保存的数据
        """
        file_path = self._get_file_path(collection)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    def create(self, collection: str, data: Dict) -> Dict:
        """创建新文档。

        Args:
            collection: 集合名称
            data: 文档数据

        Returns:
            Dict: 创建的文档
        """
        all_data = self._load_data(collection)
        
        # 添加必要的字段
        doc = data.copy()
        doc['id'] = str(uuid.uuid4())
        doc['created_at'] = datetime.now().isoformat()
        doc['updated_at'] = doc['created_at']
        
        all_data.append(doc)
        self._save_data(collection, all_data)
        return doc

    def read(self, collection: str, doc_id: str) -> Optional[Dict]:
        """读取文档。

        Args:
            collection: 集合名称
            doc_id: 文档ID

        Returns:
            Optional[Dict]: 文档数据，如果不存在则返回None
        """
        all_data = self._load_data(collection)
        for doc in all_data:
            if doc['id'] == doc_id:
                return doc
        return None

    def read_all(self, collection: str) -> List[Dict]:
        """读取集合中的所有文档。

        Args:
            collection: 集合名称

        Returns:
            List[Dict]: 所有文档
        """
        return self._load_data(collection)

    def update(self, collection: str, doc_id: str, data: Dict) -> Optional[Dict]:
        """更新文档。

        Args:
            collection: 集合名称
            doc_id: 文档ID
            data: 新的文档数据

        Returns:
            Optional[Dict]: 更新后的文档，如果文档不存在则返回None
        """
        all_data = self._load_data(collection)
        
        for i, doc in enumerate(all_data):
            if doc['id'] == doc_id:
                # 更新文档，保留原有的id和created_at
                updated_doc = data.copy()
                updated_doc['id'] = doc_id
                updated_doc['created_at'] = doc['created_at']
                updated_doc['updated_at'] = datetime.now().isoformat()
                
                all_data[i] = updated_doc
                self._save_data(collection, all_data)
                return updated_doc
        
        return None

    def delete(self, collection: str, doc_id: str) -> bool:
        """删除文档。

        Args:
            collection: 集合名称
            doc_id: 文档ID

        Returns:
            bool: 是否成功删除
        """
        all_data = self._load_data(collection)
        original_length = len(all_data)
        
        all_data = [doc for doc in all_data if doc['id'] != doc_id]
        if len(all_data) < original_length:
            self._save_data(collection, all_data)
            return True
        
        return False

    def query(self, collection: str, filter_func: callable) -> List[Dict]:
        """查询文档。

        Args:
            collection: 集合名称
            filter_func: 过滤函数

        Returns:
            List[Dict]: 符合条件的文档列表
        """
        all_data = self._load_data(collection)
        return list(filter(filter_func, all_data)) 