from typing import List, Dict
from sqlalchemy.orm import Session
from appium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import logging
import os
from datetime import datetime

from models.project import TestCase, TestResult, PageElement
from config.config import settings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestExecutor:
    def __init__(self, db: Session):
        self.db = db
        self.driver = None
        self.wait = None

    async def setup_driver(self, desired_caps: Dict):
        """设置Appium驱动"""
        try:
            self.driver = webdriver.Remote(
                f"http://{settings.APPIUM_HOST}:{settings.APPIUM_PORT}/wd/hub",
                desired_caps
            )
            self.wait = WebDriverWait(self.driver, 10)
            return True
        except Exception as e:
            logger.error(f"设置驱动失败: {str(e)}")
            return False

    def find_element(self, element: PageElement):
        """查找页面元素"""
        locator_map = {
            "id": By.ID,
            "name": By.NAME,
            "xpath": By.XPATH,
            "class_name": By.CLASS_NAME,
            "css_selector": By.CSS_SELECTOR
        }
        
        try:
            by = locator_map.get(element.element_type)
            if not by:
                raise ValueError(f"不支持的元素定位类型: {element.element_type}")
            
            return self.wait.until(
                EC.presence_of_element_located((by, element.element_value))
            )
        except Exception as e:
            logger.error(f"查找元素失败: {str(e)}")
            raise

    def execute_action(self, element, action: str, value: str = None):
        """执行元素操作"""
        try:
            if action == "click":
                element.click()
            elif action == "input":
                element.clear()
                element.send_keys(value)
            elif action == "swipe":
                # 解析滑动参数
                start_x, start_y, end_x, end_y = map(int, value.split(","))
                self.driver.swipe(start_x, start_y, end_x, end_y, 500)
            else:
                raise ValueError(f"不支持的操作类型: {action}")
        except Exception as e:
            logger.error(f"执行操作失败: {str(e)}")
            raise

    async def execute_test_case(self, test_case: TestCase) -> TestResult:
        """执行单个测试用例"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"{settings.LOG_DIR}/{test_case.id}_{timestamp}.log"
        screenshot_file = f"{settings.SCREENSHOT_DIR}/{test_case.id}_{timestamp}.png"
        
        # 确保目录存在
        os.makedirs(settings.LOG_DIR, exist_ok=True)
        os.makedirs(settings.SCREENSHOT_DIR, exist_ok=True)
        
        # 配置测试用例日志
        file_handler = logging.FileHandler(log_file)
        logger.addHandler(file_handler)
        
        try:
            for step in test_case.steps:
                element_id = step.get("element_id")
                action = step.get("action")
                value = step.get("value")
                
                if not element_id or not action:
                    raise ValueError("测试步骤缺少必要参数")
                
                # 获取页面元素
                element_db = self.db.query(PageElement).filter(
                    PageElement.id == element_id
                ).first()
                if not element_db:
                    raise ValueError(f"找不到元素ID: {element_id}")
                
                # 查找并操作元素
                element = self.find_element(element_db)
                self.execute_action(element, action, value)
                
                logger.info(f"执行步骤成功: {action} on {element_db.name}")
            
            # 测试成功
            self.driver.save_screenshot(screenshot_file)
            return TestResult(
                test_case_id=test_case.id,
                status="passed",
                screenshot_path=screenshot_file,
                log_path=log_file
            )
            
        except Exception as e:
            # 测试失败
            error_msg = str(e)
            logger.error(f"测试用例执行失败: {error_msg}")
            if self.driver:
                self.driver.save_screenshot(screenshot_file)
            
            return TestResult(
                test_case_id=test_case.id,
                status="failed",
                error_message=error_msg,
                screenshot_path=screenshot_file,
                log_path=log_file
            )
            
        finally:
            logger.removeHandler(file_handler)
            file_handler.close()
            if self.driver:
                self.driver.quit()

async def execute_test_cases(test_case_ids: List[int], db: Session) -> List[TestResult]:
    """执行多个测试用例"""
    results = []
    executor = TestExecutor(db)
    
    for test_case_id in test_case_ids:
        test_case = db.query(TestCase).filter(TestCase.id == test_case_id).first()
        if not test_case:
            logger.warning(f"找不到测试用例ID: {test_case_id}")
            continue
        
        # 设置驱动
        desired_caps = {
            "platformName": "Android",  # 或 "iOS"
            "automationName": "UiAutomator2",  # 或 "XCUITest"
            "deviceName": "Android Emulator",
            "app": "/path/to/your/app"  # 应用程序路径
        }
        
        if not await executor.setup_driver(desired_caps):
            logger.error("设置驱动失败，跳过测试用例")
            continue
        
        result = await executor.execute_test_case(test_case)
        results.append(result)
        
        # 保存测试结果
        db.add(result)
        db.commit()
    
    return results 