import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.image import MIMEImage
from typing import List
from datetime import datetime
import os
import pandas as pd
from jinja2 import Template

from models.project import TestResult
from config.config import settings

async def generate_test_report(results: List[TestResult]) -> str:
    """生成测试报告HTML"""
    # 统计测试结果
    total = len(results)
    passed = len([r for r in results if r.status == "passed"])
    failed = total - passed
    pass_rate = (passed / total * 100) if total > 0 else 0

    # 创建结果数据表
    data = []
    for result in results:
        data.append({
            "测试用例ID": result.test_case_id,
            "状态": "通过" if result.status == "passed" else "失败",
            "错误信息": result.error_message or "-",
            "执行时间": result.execution_time.strftime("%Y-%m-%d %H:%M:%S")
        })
    
    df = pd.DataFrame(data)
    table_html = df.to_html(classes="table table-striped", index=False)

    # 使用模板生成报告
    template_str = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>自动化测试报告</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { margin-bottom: 20px; }
            .summary { margin-bottom: 30px; }
            .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            .table th, .table td { padding: 8px; border: 1px solid #ddd; }
            .table th { background-color: #f5f5f5; }
            .passed { color: green; }
            .failed { color: red; }
        </style>
    </head>
    <body>
        <div class="header">
            <h2>自动化测试报告</h2>
            <p>执行时间: {{ execution_time }}</p>
        </div>
        <div class="summary">
            <h3>测试结果统计</h3>
            <p>总用例数: {{ total }}</p>
            <p>通过数: <span class="passed">{{ passed }}</span></p>
            <p>失败数: <span class="failed">{{ failed }}</span></p>
            <p>通过率: {{ "%.2f"|format(pass_rate) }}%</p>
        </div>
        <div class="details">
            <h3>详细结果</h3>
            {{ table_html }}
        </div>
    </body>
    </html>
    """

    template = Template(template_str)
    html = template.render(
        execution_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        total=total,
        passed=passed,
        failed=failed,
        pass_rate=pass_rate,
        table_html=table_html
    )

    return html

async def send_test_report(results: List[TestResult], recipients: List[str]):
    """发送测试报告邮件"""
    # 生成报告
    html_content = await generate_test_report(results)
    
    # 创建邮件
    msg = MIMEMultipart()
    msg["Subject"] = f"自动化测试报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    msg["From"] = settings.SMTP_USER
    msg["To"] = ", ".join(recipients)
    
    # 添加HTML内容
    msg.attach(MIMEText(html_content, "html"))
    
    # 添加截图附件
    for result in results:
        if result.screenshot_path and os.path.exists(result.screenshot_path):
            with open(result.screenshot_path, "rb") as f:
                img = MIMEImage(f.read())
                img.add_header("Content-ID", f"<{os.path.basename(result.screenshot_path)}>")
                img.add_header("Content-Disposition", "attachment", 
                             filename=os.path.basename(result.screenshot_path))
                msg.attach(img)
    
    # 发送邮件
    try:
        with smtplib.SMTP(settings.SMTP_SERVER, settings.SMTP_PORT) as server:
            server.starttls()
            server.login(settings.SMTP_USER, settings.SMTP_PASSWORD)
            server.send_message(msg)
        return True
    except Exception as e:
        print(f"发送邮件失败: {str(e)}")
        return False 