from appium import webdriver
from appium.webdriver.common.mobileby import MobileBy
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException, WebDriverException
import uuid
import json
import os
import subprocess
import time

class DeviceManager:
    def __init__(self):
        self.driver = None
        self.elements_cache = {}
        self.current_package = None
        self.max_retries = 3
        self.retry_delay = 2
        self._progress_callback = None

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self._progress_callback = callback

    def _report_progress(self, message, progress=None):
        """报告进度"""
        if self._progress_callback:
            self._progress_callback({
                "message": message,
                "progress": progress
            })
        print(f"进度: {message}")

    def _ensure_driver_connected(self, package_name=None, main_activity=None):
        """确保driver已连接"""
        try:
            if self.driver:
                # 如果driver已存在，检查当前应用是否是目标应用
                current_package = self.driver.current_package
                if package_name and current_package != package_name:
                    # 如果需要切换应用，则重新连接
                    self._report_progress("正在切换应用...", 10)
                    self.connect_to_device(package_name, main_activity)
                else:
                    # 验证连接是否有效
                    try:
                        self.driver.current_activity
                        return  # 连接有效，直接返回
                    except:
                        # 连接无效，需要重新连接
                        self._report_progress("连接已断开，正在重新连接...", 10)
                        self.connect_to_device(package_name, main_activity)
            else:
                # 首次连接
                self._report_progress("正在连接设备...", 10)
                self.connect_to_device(package_name, main_activity)
        except Exception as e:
            self._report_progress(f"连接检查失败: {str(e)}", 10)
            self.connect_to_device(package_name, main_activity)

    def connect_to_device(self, package_name=None, main_activity=None):
        """连接到设备并启动指定的应用"""
        retry_count = 0
        last_error = None
        
        # 确保Appium服务器正在运行
        try:
            self._report_progress("正在检查Appium服务器状态...", 10)
            import requests
            response = requests.get('http://localhost:4723/wd/hub/status')
            if response.status_code != 200:
                raise Exception("Appium服务器未响应")
        except Exception as e:
            raise Exception(f"无法连接到Appium服务器，请确保Appium服务器正在运行: {str(e)}")
        
        while retry_count < self.max_retries:
            try:
                if self.driver:
                    self._report_progress("正在关闭现有连接...", 20)
                    try:
                        self.driver.quit()
                    except:
                        pass
                    self.driver = None
                
                # 检查设备连接状态
                self._report_progress("正在检查设备连接状态...", 30)
                result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
                if result.returncode != 0:
                    raise Exception(f"执行adb devices失败: {result.stderr}")
                
                devices = [line.split('\t') for line in result.stdout.strip().split('\n')[1:] if line.strip()]
                if not devices:
                    raise Exception("未检测到已连接的Android设备")
                
                device_id = devices[0][0]
                if not device_id or devices[0][1] != 'device':
                    raise Exception("设备未正确连接或未授权")
                
                # 重置UiAutomator2服务
                self._report_progress("正在重置UiAutomator2服务...", 35)
                try:
                    subprocess.run(['adb', 'uninstall', 'io.appium.uiautomator2.server'], capture_output=True)
                    subprocess.run(['adb', 'uninstall', 'io.appium.uiautomator2.server.test'], capture_output=True)
                except:
                    pass

                # 停止并清除目标应用
                if package_name:
                    self._report_progress(f"正在重置应用 {package_name}...", 40)
                    try:
                        subprocess.run(['adb', 'shell', 'am', 'force-stop', package_name], capture_output=True)
                        subprocess.run(['adb', 'shell', 'pm', 'clear', package_name], capture_output=True)
                    except:
                        pass
                
                self._report_progress("正在准备连接参数...", 45)
                caps = {
                    'platformName': 'Android',
                    'automationName': 'UiAutomator2',
                    'noReset': True,
                    'newCommandTimeout': 6000,
                    'udid': device_id,
                    'autoGrantPermissions': True,
                    'skipServerInstallation': False,
                    'skipDeviceInitialization': False,
                    'uiautomator2ServerInstallTimeout': 60000,
                    'androidInstallTimeout': 90000,
                    'adbExecTimeout': 60000
                }
                
                if package_name:
                    self._report_progress(f"正在获取应用 {package_name} 的信息...", 50)
                    caps['appPackage'] = package_name
                    
                    # 检查应用是否已安装
                    check_app = subprocess.run(
                        ['adb', 'shell', 'pm', 'list', 'packages', package_name],
                        capture_output=True, text=True
                    )
                    
                    if package_name not in check_app.stdout:
                        raise Exception(f"应用 {package_name} 未安装")
                    
                    # 如果提供了主Activity，直接使用
                    if main_activity:
                        self._report_progress(f"使用指定的主Activity: {main_activity}", 60)
                        if main_activity.startswith('.'):
                            main_activity = package_name + main_activity
                        caps['appActivity'] = main_activity
                    else:
                        # 如果是首次连接，才需要查找主Activity
                        if not self.current_package:
                            self._report_progress("正在查找应用入口...", 60)
                            result = subprocess.run(
                                ['adb', 'shell', 'dumpsys', 'package', package_name],
                                capture_output=True, text=True
                            )
                            
                            # 查找主Activity
                            main_activity = None
                            lines = result.stdout.split('\n')
                            for i, line in enumerate(lines):
                                if 'android.intent.action.MAIN' in line:
                                    # 查找下一个包含Activity的行
                                    for next_line in lines[i+1:]:
                                        if 'Activity' in next_line:
                                            main_activity = next_line.strip().split()[-1]
                                            break
                                    if main_activity:
                                        break
                            
                            if main_activity:
                                if main_activity.startswith('.'):
                                    main_activity = package_name + main_activity
                                caps['appActivity'] = main_activity
                                self._report_progress(f"找到主Activity: {main_activity}", 65)
                            else:
                                # 如果找不到主Activity，尝试直接启动应用
                                self._report_progress("未找到主Activity，尝试直接启动应用...", 65)
                                subprocess.run(['adb', 'shell', 'monkey', '-p', package_name, '-c', 'android.intent.category.LAUNCHER', '1'])
                
                self._report_progress("正在连接到设备...", 70)
                try:
                    self.driver = webdriver.Remote('http://localhost:4723/wd/hub', caps)
                except Exception as e:
                    if 'Failed to start Appium UiAutomator2 Server' in str(e):
                        # 如果UiAutomator2服务器启动失败，尝试重新安装
                        self._report_progress("UiAutomator2服务器启动失败，尝试重新安装...", 75)
                        subprocess.run(['adb', 'uninstall', 'io.appium.uiautomator2.server'])
                        subprocess.run(['adb', 'uninstall', 'io.appium.uiautomator2.server.test'])
                        time.sleep(2)  # 等待卸载完成
                        self.driver = webdriver.Remote('http://localhost:4723/wd/hub', caps)
                    else:
                        raise
                
                self._report_progress("正在等待应用启动...", 80)
                # 等待应用启动
                time.sleep(5)  # 增加等待时间
                
                # 验证连接是否成功
                try:
                    self.driver.current_activity
                    self._report_progress("连接成功！", 100)
                    self.current_package = package_name
                    return
                except Exception as e:
                    raise Exception(f"连接验证失败: {str(e)}")
                
            except Exception as e:
                last_error = e
                retry_count += 1
                if retry_count < self.max_retries:
                    self._report_progress(f"连接失败，正在重试 ({retry_count}/{self.max_retries}): {str(e)}", (retry_count / self.max_retries) * 100)
                    time.sleep(self.retry_delay)
                    continue
        
        error_msg = f"无法连接到设备或启动应用: {str(last_error)}"
        self._report_progress(error_msg, 100)
        raise Exception(error_msg)

    def get_elements(self, package_name=None, main_activity=None):
        """获取当前页面上的所有元素"""
        try:
            self._report_progress("正在检查设备连接...", 10)
            self._ensure_driver_connected(package_name, main_activity)
            
            self._report_progress("正在等待页面加载...", 30)
            # 等待页面加载
            time.sleep(1)
            
            elements = []
            try:
                self._report_progress("正在获取页面元素...", 50)
                # 获取所有可见元素
                visible_elements = self.driver.find_elements(MobileBy.XPATH, "//*[@displayed='true']")
                
                total_elements = len(visible_elements)
                self._report_progress(f"找到 {total_elements} 个可见元素", 50)
                
                for idx, element in enumerate(visible_elements, 1):
                    try:
                        progress = 50 + (idx / total_elements) * 50
                        
                        element_id = str(uuid.uuid4())
                        element_info = {
                            'id': element_id,
                            'type': element.get_attribute('class'),
                            'text': element.text or element.get_attribute('content-desc'),
                            'resource_id': element.get_attribute('resource-id'),
                            'package': element.get_attribute('package'),
                            'xpath': self._generate_xpath(element),
                            'bounds': element.get_attribute('bounds'),
                            'clickable': element.get_attribute('clickable') == 'true',
                            'focused': element.get_attribute('focused') == 'true'
                        }
                        
                        # 生成详细的元素信息日志
                        element_desc = []
                        if element_info['resource_id']:
                            element_desc.append(f"资源ID: {element_info['resource_id']}")
                        if element_info['text']:
                            element_desc.append(f"文本: {element_info['text']}")
                        if element_info['type']:
                            element_desc.append(f"类型: {element_info['type']}")
                        
                        log_message = f"处理元素 {idx}/{total_elements}"
                        if element_desc:
                            log_message += f" ({', '.join(element_desc)})"
                        
                        # 发送进度和元素信息
                        if self._progress_callback:
                            self._progress_callback({
                                "message": log_message,
                                "progress": progress,
                                "element": element_info
                            })
                        print(f"进度: {log_message}")
                        
                        # 缓存元素信息
                        self.elements_cache[element_id] = {
                            'element': element,
                            'info': element_info
                        }
                        
                        elements.append(element_info)
                            
                    except Exception as e:
                        error_msg = f"处理元素 {idx} 时出错: {str(e)}"
                        self._report_progress(error_msg, progress)
                        continue
                
                self._report_progress("元素获取完成！", 100)
            except Exception as e:
                error_msg = f"获取元素列表时出错: {str(e)}"
                self._report_progress(error_msg, 100)
                raise
            
            return elements
            
        except WebDriverException as e:
            error_msg = f"WebDriver异常: {str(e)}"
            self._report_progress(error_msg, 100)
            # 如果WebDriver会话失效，尝试重新连接
            self.connect_to_device(package_name, main_activity)
            return self.get_elements(package_name, main_activity)
        except Exception as e:
            error_msg = f"获取元素时出错: {str(e)}"
            self._report_progress(error_msg, 100)
            raise Exception(f"获取元素失败: {str(e)}")

    def capture_element(self, package_name=None, main_activity=None):
        """捕获用户点击的元素"""
        try:
            self._report_progress("正在检查设备连接...", 10)
            self._ensure_driver_connected(package_name, main_activity)
            
            self._report_progress("等待用户点击元素...\n提示：请在设备上点击要捕获的元素", 30)
            # 等待用户点击元素（这里可以根据实际需求修改等待时间）
            element = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((MobileBy.XPATH, "//*[@clickable='true']"))
            )
            
            self._report_progress("正在获取元素信息...", 60)
            element_id = str(uuid.uuid4())
            element_info = {
                'id': element_id,
                'type': element.get_attribute('class'),
                'text': element.text or element.get_attribute('content-desc'),
                'resource_id': element.get_attribute('resource-id'),
                'package': element.get_attribute('package'),
                'xpath': self._generate_xpath(element),
                'bounds': element.get_attribute('bounds'),
                'clickable': element.get_attribute('clickable') == 'true',
                'focused': element.get_attribute('focused') == 'true'
            }
            
            self._report_progress("正在保存元素信息...", 80)
            # 缓存元素信息
            self.elements_cache[element_id] = {
                'element': element,
                'info': element_info
            }
            
            self._report_progress("元素捕获成功！", 100)
            return element_info
            
        except WebDriverException:
            # 如果WebDriver会话失效，尝试重新连接
            self.connect_to_device(package_name, main_activity)
            return self.capture_element(package_name, main_activity)
        except TimeoutException:
            self._report_progress("等待超时，未检测到元素点击", 100)
            raise Exception("等待超时，未检测到元素点击")
        except Exception as e:
            self._report_progress(f"捕获元素失败: {str(e)}", 100)
            raise Exception(f"捕获元素失败: {str(e)}")

    def highlight_element(self, element_id):
        """高亮显示指定的元素"""
        try:
            if element_id not in self.elements_cache:
                raise Exception("元素不存在或已过期")
            
            element = self.elements_cache[element_id]['element']
            
            # 使用JavaScript修改元素样式来实现高亮效果
            self.driver.execute_script(
                "arguments[0].style.border='2px solid red';"
                "arguments[0].style.backgroundColor='yellow';"
                "setTimeout(function(){"
                "arguments[0].style.border='';"
                "arguments[0].style.backgroundColor='';"
                "}, 2000);", element
            )
        except Exception as e:
            raise Exception(f"高亮元素失败: {str(e)}")

    def _generate_xpath(self, element):
        """生成元素的XPath路径"""
        try:
            # 获取元素的属性
            resource_id = element.get_attribute('resource-id')
            text = element.text
            content_desc = element.get_attribute('content-desc')
            class_name = element.get_attribute('class')
            package = element.get_attribute('package')
            
            # 构建XPath，优先使用唯一标识符
            if resource_id:
                return f"//*[@resource-id='{resource_id}']"
            elif text:
                return f"//*[@text='{text}']"
            elif content_desc:
                return f"//*[@content-desc='{content_desc}']"
            elif package and class_name:
                return f"//*[@package='{package}' and @class='{class_name}']"
            else:
                return f"//*[@class='{class_name}']"
        except Exception:
            return "未能生成XPath"

    def get_installed_packages(self):
        """获取设备上已安装的应用包名列表"""
        try:
            self._report_progress("正在获取已安装应用列表...", 50)
            result = subprocess.run(['adb', 'shell', 'pm', 'list', 'packages', '-3'], 
                                 capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception(f"获取应用列表失败: {result.stderr}")
                
            packages = result.stdout.strip().split('\n')
            package_list = [pkg.replace('package:', '').strip() for pkg in packages if pkg.strip()]
            
            self._report_progress("应用列表获取完成！", 100)
            return package_list
        except Exception as e:
            self._report_progress(f"获取已安装应用列表失败: {str(e)}", 100)
            print(f"获取已安装应用列表失败: {str(e)}")
            return []

    def __del__(self):
        """清理资源"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass 