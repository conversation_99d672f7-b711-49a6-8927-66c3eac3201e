from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String, DateTime, Foreign<PERSON>ey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import db
from datetime import datetime

class Project(db.Model):
    __tablename__ = "projects"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(String(500))
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    test_cases = relationship("TestCase", back_populates="project")
    elements = relationship("PageElement", back_populates="project")

class TestCase(db.Model):
    __tablename__ = "test_cases"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(String)
    project_id = Column(Integer, Foreign<PERSON><PERSON>("projects.id"))
    steps = Column(JSON)  # 测试步骤，存储为JSON格式
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    project = relationship("Project", back_populates="test_cases")
    test_results = relationship("TestResult", back_populates="test_case")

class PageElement(db.Model):
    __tablename__ = "page_elements"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"))
    element_type = Column(String)  # id, name, xpath, etc.
    element_value = Column(String)
    description = Column(String)
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    project = relationship("Project", back_populates="elements")

class TestResult(db.Model):
    __tablename__ = "test_results"

    id = Column(Integer, primary_key=True, index=True)
    test_case_id = Column(Integer, ForeignKey("test_cases.id"))
    status = Column(String)  # passed, failed, error
    error_message = Column(String, nullable=True)
    screenshot_path = Column(String, nullable=True)
    log_path = Column(String, nullable=True)
    execution_time = Column(DateTime(timezone=True), server_default=func.now())
    
    test_case = relationship("TestCase", back_populates="test_results") 