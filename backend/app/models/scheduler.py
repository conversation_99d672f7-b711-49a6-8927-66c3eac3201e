from sqlalchemy import Column, Integer, String, <PERSON>ole<PERSON>, DateTime, ForeignKey, JSON
from sqlalchemy.sql import func
from .base import Base

class ScheduledTask(Base):
    __tablename__ = "scheduled_tasks"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True)
    description = Column(String)
    project_id = Column(Integer, ForeignKey("projects.id"))
    test_case_ids = Column(JSON)  # 存储测试用例ID列表
    cron_expression = Column(String)  # Cron表达式
    is_active = Column(Boolean, default=True)
    notification_emails = Column(JSON)  # 通知邮箱列表
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_run = Column(DateTime(timezone=True), nullable=True)
    next_run = Column(DateTime(timezone=True), nullable=True) 