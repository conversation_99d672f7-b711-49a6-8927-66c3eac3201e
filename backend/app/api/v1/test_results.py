from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile
from sqlalchemy.orm import Session
from typing import List
import os
import aiofiles
from datetime import datetime

from models.base import get_db
from models.project import TestResult
from models.user import User
from schemas.test_result import TestResultCreate, TestResultResponse
from api.deps import get_current_active_user
from config.config import settings

router = APIRouter()

@router.post("/test-results/", response_model=TestResultResponse)
async def create_test_result(
    test_result: TestResultCreate,
    screenshot: UploadFile = File(None),
    log_file: UploadFile = File(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    # 创建存储目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    screenshot_path = None
    log_path = None

    if screenshot:
        os.makedirs(settings.SCREENSHOT_DIR, exist_ok=True)
        screenshot_path = f"{settings.SCREENSHOT_DIR}/{test_result.test_case_id}_{timestamp}.png"
        async with aiofiles.open(screenshot_path, 'wb') as f:
            await f.write(await screenshot.read())

    if log_file:
        os.makedirs(settings.LOG_DIR, exist_ok=True)
        log_path = f"{settings.LOG_DIR}/{test_result.test_case_id}_{timestamp}.log"
        async with aiofiles.open(log_path, 'wb') as f:
            await f.write(await log_file.read())

    db_test_result = TestResult(
        test_case_id=test_result.test_case_id,
        status=test_result.status,
        error_message=test_result.error_message,
        screenshot_path=screenshot_path,
        log_path=log_path
    )
    db.add(db_test_result)
    db.commit()
    db.refresh(db_test_result)
    return db_test_result

@router.get("/test-results/", response_model=List[TestResultResponse])
def read_test_results(
    skip: int = 0,
    limit: int = 100,
    test_case_id: int = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    query = db.query(TestResult)
    if test_case_id:
        query = query.filter(TestResult.test_case_id == test_case_id)
    test_results = query.order_by(TestResult.execution_time.desc()).offset(skip).limit(limit).all()
    return test_results

@router.get("/test-results/{result_id}", response_model=TestResultResponse)
def read_test_result(
    result_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    test_result = db.query(TestResult).filter(TestResult.id == result_id).first()
    if not test_result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="测试结果不存在"
        )
    return test_result

@router.delete("/test-results/{result_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_test_result(
    result_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    test_result = db.query(TestResult).filter(TestResult.id == result_id).first()
    if not test_result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="测试结果不存在"
        )
    
    # 删除相关文件
    if test_result.screenshot_path and os.path.exists(test_result.screenshot_path):
        os.remove(test_result.screenshot_path)
    if test_result.log_path and os.path.exists(test_result.log_path):
        os.remove(test_result.log_path)
    
    db.delete(test_result)
    db.commit()
    return None 