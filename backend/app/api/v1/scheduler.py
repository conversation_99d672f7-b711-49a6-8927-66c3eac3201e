from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List
from datetime import datetime
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger

from models.base import get_db
from models.scheduler import ScheduledTask
from models.user import User
from schemas.scheduler import ScheduledTaskCreate, ScheduledTaskUpdate, ScheduledTaskResponse
from api.deps import get_current_active_user
from core.test_executor import execute_test_cases
from core.email import send_test_report

router = APIRouter()
scheduler = BackgroundScheduler()
scheduler.start()

@router.post("/scheduled-tasks/", response_model=ScheduledTaskResponse)
def create_scheduled_task(
    task: ScheduledTaskCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    db_task = ScheduledTask(
        name=task.name,
        description=task.description,
        project_id=task.project_id,
        test_case_ids=task.test_case_ids,
        cron_expression=task.cron_expression,
        notification_emails=task.notification_emails,
        created_by=current_user.id
    )
    db.add(db_task)
    db.commit()
    db.refresh(db_task)

    # 添加定时任务
    if db_task.is_active:
        scheduler.add_job(
            execute_and_notify,
            CronTrigger.from_crontab(db_task.cron_expression),
            id=f"task_{db_task.id}",
            args=[db_task.id, db],
            replace_existing=True
        )

    return db_task

@router.get("/scheduled-tasks/", response_model=List[ScheduledTaskResponse])
def read_scheduled_tasks(
    skip: int = 0,
    limit: int = 100,
    project_id: int = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    query = db.query(ScheduledTask)
    if project_id:
        query = query.filter(ScheduledTask.project_id == project_id)
    tasks = query.offset(skip).limit(limit).all()
    return tasks

@router.get("/scheduled-tasks/{task_id}", response_model=ScheduledTaskResponse)
def read_scheduled_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    task = db.query(ScheduledTask).filter(ScheduledTask.id == task_id).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="定时任务不存在"
        )
    return task

@router.put("/scheduled-tasks/{task_id}", response_model=ScheduledTaskResponse)
def update_scheduled_task(
    task_id: int,
    task: ScheduledTaskUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    db_task = db.query(ScheduledTask).filter(ScheduledTask.id == task_id).first()
    if not db_task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="定时任务不存在"
        )
    
    for field, value in task.dict(exclude_unset=True).items():
        setattr(db_task, field, value)
    
    db.commit()
    db.refresh(db_task)

    # 更新定时任务
    job_id = f"task_{db_task.id}"
    if db_task.is_active:
        scheduler.add_job(
            execute_and_notify,
            CronTrigger.from_crontab(db_task.cron_expression),
            id=job_id,
            args=[db_task.id, db],
            replace_existing=True
        )
    else:
        if scheduler.get_job(job_id):
            scheduler.remove_job(job_id)

    return db_task

@router.delete("/scheduled-tasks/{task_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_scheduled_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    db_task = db.query(ScheduledTask).filter(ScheduledTask.id == task_id).first()
    if not db_task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="定时任务不存在"
        )
    
    # 删除定时任务
    job_id = f"task_{db_task.id}"
    if scheduler.get_job(job_id):
        scheduler.remove_job(job_id)
    
    db.delete(db_task)
    db.commit()
    return None

async def execute_and_notify(task_id: int, db: Session):
    """执行测试用例并发送通知"""
    task = db.query(ScheduledTask).filter(ScheduledTask.id == task_id).first()
    if not task:
        return

    # 更新任务执行时间
    task.last_run = datetime.now()
    db.commit()

    # 执行测试用例
    results = await execute_test_cases(task.test_case_ids, db)

    # 发送测试报告
    if task.notification_emails:
        await send_test_report(results, task.notification_emails)

    # 更新下次执行时间
    trigger = CronTrigger.from_crontab(task.cron_expression)
    task.next_run = trigger.get_next_fire_time(None, datetime.now())
    db.commit() 