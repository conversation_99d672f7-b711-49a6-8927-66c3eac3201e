from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from models.base import get_db
from models.project import PageElement
from models.user import User
from schemas.element import ElementCreate, ElementUpdate, ElementResponse
from api.deps import get_current_active_user

router = APIRouter()

@router.post("/elements/", response_model=ElementResponse)
def create_element(
    element: ElementCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    db_element = PageElement(
        name=element.name,
        project_id=element.project_id,
        element_type=element.element_type,
        element_value=element.element_value,
        description=element.description,
        created_by=current_user.id
    )
    db.add(db_element)
    db.commit()
    db.refresh(db_element)
    return db_element

@router.get("/elements/", response_model=List[ElementResponse])
def read_elements(
    skip: int = 0,
    limit: int = 100,
    project_id: int = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    query = db.query(PageElement)
    if project_id:
        query = query.filter(PageElement.project_id == project_id)
    elements = query.offset(skip).limit(limit).all()
    return elements

@router.get("/elements/{element_id}", response_model=ElementResponse)
def read_element(
    element_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    element = db.query(PageElement).filter(PageElement.id == element_id).first()
    if not element:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="页面元素不存在"
        )
    return element

@router.put("/elements/{element_id}", response_model=ElementResponse)
def update_element(
    element_id: int,
    element: ElementUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    db_element = db.query(PageElement).filter(PageElement.id == element_id).first()
    if not db_element:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="页面元素不存在"
        )
    
    for field, value in element.dict(exclude_unset=True).items():
        setattr(db_element, field, value)
    
    db.commit()
    db.refresh(db_element)
    return db_element

@router.delete("/elements/{element_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_element(
    element_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    db_element = db.query(PageElement).filter(PageElement.id == element_id).first()
    if not db_element:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="页面元素不存在"
        )
    db.delete(db_element)
    db.commit()
    return None 