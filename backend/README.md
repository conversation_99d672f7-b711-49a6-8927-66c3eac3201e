# 自动化测试管理系统 - 后端

本目录包含自动化测试管理系统的后端代码。

## 技术栈

- FastAPI：高性能的现代Python Web框架
- SQLAlchemy：Python SQL工具包和ORM
- Pydantic：数据验证和序列化
- Appium：移动应用自动化测试框架
- APScheduler：Python任务调度框架

## 目录结构

```
backend/
├── app/
│   ├── api/              # API路由
│   │   └── v1/          # API版本1
│   ├── core/            # 核心功能
│   │   ├── security.py  # 安全相关
│   │   ├── email.py     # 邮件功能
│   │   └── executor.py  # 测试执行器
│   ├── models/          # 数据库模型
│   │   ├── base.py
│   │   ├── user.py
│   │   └── project.py
│   └── schemas/         # Pydantic模型
│       ├── user.py
│       └── project.py
├── config/              # 配置文件
│   └── config.py
├── tests/              # 测试用例
├── requirements.txt    # 依赖文件
└── README.md          # 说明文档
```

## API路由说明

1. 认证相关 (/api/v1/auth)
   - POST /token：获取访问令牌
   
2. 用户管理 (/api/v1/users)
   - POST /：创建用户
   - GET /：获取用户列表
   - GET /me：获取当前用户信息
   - PUT /{user_id}：更新用户信息
   - DELETE /{user_id}：删除用户

3. 项目管理 (/api/v1/projects)
   - POST /：创建项目
   - GET /：获取项目列表
   - GET /{project_id}：获取项目详情
   - PUT /{project_id}：更新项目
   - DELETE /{project_id}：删除项目

4. 测试用例管理 (/api/v1/test-cases)
   - POST /：创建测试用例
   - GET /：获取测试用例列表
   - GET /{test_case_id}：获取测试用例详情
   - PUT /{test_case_id}：更新测试用例
   - DELETE /{test_case_id}：删除测试用例

5. 页面元素管理 (/api/v1/elements)
   - POST /：创建页面元素
   - GET /：获取元素列表
   - GET /{element_id}：获取元素详情
   - PUT /{element_id}：更新元素
   - DELETE /{element_id}：删除元素

6. 测试结果管理 (/api/v1/test-results)
   - POST /：创建测试结果
   - GET /：获取测试结果列表
   - GET /{result_id}：获取测试结果详情
   - DELETE /{result_id}：删除测试结果

7. 定时任务管理 (/api/v1/scheduled-tasks)
   - POST /：创建定时任务
   - GET /：获取任务列表
   - GET /{task_id}：获取任务详情
   - PUT /{task_id}：更新任务
   - DELETE /{task_id}：删除任务

## 开发环境设置

1. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
.\venv\Scripts\activate  # Windows
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 配置环境变量
创建 `.env` 文件并设置必要的环境变量。

4. 初始化数据库
```bash
# 使用psql或其他工具创建数据库
```

5. 启动开发服务器
```bash
uvicorn app.main:app --reload --port 8001
```

## 测试

运行测试：
```bash
pytest
```

## 部署

1. 构建Docker镜像
```bash
docker build -t auto-test-backend .
```

2. 运行容器
```bash
docker run -d -p 8001:8001 auto-test-backend
```

## 开发规范

1. 代码风格
   - 遵循PEP 8规范
   - 使用black进行代码格式化
   - 使用isort排序导入

2. 文档规范
   - 所有函数都应该有文档字符串
   - API端点应该有完整的参数说明
   - 使用类型注解

3. 测试规范
   - 新功能必须包含测试
   - 保持测试覆盖率
   - 使用pytest进行测试

## 调试

1. 日志
   - 日志文件位于logs目录
   - 使用不同的日志级别
   - 包含详细的错误信息

2. 调试工具
   - 使用FastAPI的调试模式
   - 使用pytest的调试功能
   - 使用Python调试器pdb

## 性能优化

1. 数据库
   - 使用适当的索引
   - 优化查询语句
   - 使用缓存

2. API
   - 使用异步操作
   - 实现分页
   - 减少不必要的查询

## 安全性

1. 认证
   - 使用JWT令牌
   - 实现令牌刷新
   - 密码加密存储

2. 授权
   - 基于角色的访问控制
   - API权限验证
   - 输入验证

3. 数据安全
   - 敏感数据加密
   - 安全的文件处理
   - SQL注入防护

## 维护

1. 日常维护
   - 日志轮转
   - 数据库备份
   - 系统监控

2. 更新
   - 依赖包更新
   - 安全补丁
   - 功能升级

## 故障排除

常见问题及解决方案：

1. 数据库连接问题
   - 检查数据库服务状态
   - 验证连接参数
   - 检查网络连接

2. API错误
   - 检查请求参数
   - 查看错误日志
   - 验证认证信息

3. 测试执行问题
   - 检查Appium服务
   - 验证设备连接
   - 检查测试环境 