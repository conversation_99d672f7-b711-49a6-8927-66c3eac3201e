# 🎯 智能索引分配功能说明

## 问题描述
在您的登录界面中，有两个输入框都使用相同的ID：`com.olight.omall:id/edt_view`
- 第一个输入框：用户名/手机号
- 第二个输入框：密码

## 🚀 解决方案：智能索引分配

### 核心机制
系统会**自动按照步骤顺序**为相同ID的元素分配索引：
- 第一次使用该ID → 自动分配索引 0
- 第二次使用该ID → 自动分配索引 1
- 第三次使用该ID → 自动分配索引 2
- 以此类推...

### 📋 测试用例示例

```json
{
  "name": "用户登录测试",
  "steps": [
    {
      "type": "set_text",
      "params": {
        "selectorType": "id",
        "selectorValue": "com.olight.omall:id/edt_view",
        "inputValue": "13800138000"
      },
      "description": "输入用户名"
    },
    {
      "type": "set_text", 
      "params": {
        "selectorType": "id",
        "selectorValue": "com.olight.omall:id/edt_view",
        "inputValue": "password123"
      },
      "description": "输入密码"
    },
    {
      "type": "click",
      "params": {
        "selectorType": "text",
        "selectorValue": "登录"
      },
      "description": "点击登录按钮"
    }
  ]
}
```

### 🔄 执行过程

1. **步骤1**：设置第一个输入框文本
   - ID: `com.olight.omall:id/edt_view`
   - 系统自动分配：`elementIndex = 0`
   - 结果：在**第一个输入框**中输入 "13800138000"

2. **步骤2**：设置第二个输入框文本
   - ID: `com.olight.omall:id/edt_view`（相同ID）
   - 系统自动分配：`elementIndex = 1`
   - 结果：在**第二个输入框**中输入 "password123"

3. **步骤3**：点击登录按钮
   - 文本选择器：找到"登录"按钮并点击

### 📊 日志输出示例

```
[智能索引] 为ID 'com.olight.omall:id/edt_view' 自动分配索引: 0
[DEBUG] set_text step: {...}
[DEBUG] set_text params: {'selectorType': 'id', 'selectorValue': 'com.olight.omall:id/edt_view', 'inputValue': '13800138000', 'elementIndex': 0}

[智能索引] 为ID 'com.olight.omall:id/edt_view' 自动分配索引: 1
[DEBUG] set_text step: {...}
[DEBUG] set_text params: {'selectorType': 'id', 'selectorValue': 'com.olight.omall:id/edt_view', 'inputValue': 'password123', 'elementIndex': 1}
```

### ✅ 优势

1. **自动化**：无需手动指定索引
2. **顺序性**：按照步骤顺序自动分配
3. **准确性**：确保每个步骤操作正确的元素
4. **兼容性**：支持现有的手动索引指定方式

### 🎯 使用建议

1. **简单场景**：直接使用ID选择器，让系统自动分配索引
2. **复杂场景**：如果需要跳过某个元素，可以手动指定 `elementIndex`
3. **调试模式**：查看日志中的索引分配信息来验证正确性

这样就完美解决了相同ID元素的精确操作问题！🎉
